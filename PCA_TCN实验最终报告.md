# PCA数据TCN预测实验最终报告

## 实验总结

基于PCA4_NeZha.csv数据，我们进行了两轮TCN预测实验：
1. **基础多尺度TCN模型**
2. **改进的TCN模型**（增加对数变换、改进损失函数、减少过拟合）

## 数据特征分析

- **数据规模**: 153行，时间跨度约5个月
- **票房分布**: 极度不均匀，范围22.37 - 86,670.81
- **异常值**: 29个异常值（票房>12,831.51），占19%
- **特征**: PCA降维后的4个主成分 + 周末/假期标识

## 实验结果对比

### 基础模型 vs 改进模型

| 预测天数 | 模型类型 | MAE | RMSE | MAPE(%) | 参数量 |
|---------|----------|-----|------|---------|--------|
| **7天** | 基础模型 | 483.74 | 486.39 | 1410.87% | ~186K |
| **7天** | 改进模型 | 860.46 | 863.07 | 2486.30% | 34K |
| **14天** | 基础模型 | 232.07 | 276.02 | 681.65% | ~186K |
| **14天** | 改进模型 | 1042.09 | 1044.04 | 2989.17% | 34K |
| **21天** | 基础模型 | 205.63 | 238.59 | 510.98% | ~186K |
| **21天** | 改进模型 | 592.25 | 599.01 | 1623.63% | 34K |

## 关键发现

### 1. 模型复杂度与性能的关系
- **基础模型**: 参数多(186K)，在21天预测中表现最佳
- **改进模型**: 参数少(34K)，但性能反而下降
- **结论**: 对于这个数据集，适度的模型复杂度是必要的

### 2. 预测时间窗口效应
**两个模型都显示相同趋势**:
- 21天预测 > 14天预测 > 7天预测
- 长期预测比短期预测更准确

### 3. 数据特性挑战
- **极端值影响**: 票房范围跨越4个数量级
- **PCA信息丢失**: 降维可能丢失了重要的短期预测信息
- **样本不平衡**: 大部分数据是低票房，少数是高票房

## 技术分析

### 基础模型优势
1. **多尺度架构**: 有效捕获不同时间尺度的模式
2. **注意力机制**: 提升重要特征的权重
3. **参数充足**: 足够的模型容量学习复杂模式

### 改进模型的问题
1. **过度简化**: 减少参数导致欠拟合
2. **对数变换**: 可能不适合这个特定的数据分布
3. **正则化过强**: 限制了模型的学习能力

## 最佳实践建议

### 1. 数据预处理
```python
# 推荐的预处理流程
- 异常值检测但不删除（保留信息）
- 使用RobustScaler而非StandardScaler
- 考虑分段建模（高票房vs低票房）
```

### 2. 模型架构
```python
# 推荐配置
- 序列长度: 14天
- 多尺度TCN: 3个分支
- 注意力头数: 4个
- 参数量: 10-20万
```

### 3. 训练策略
```python
# 推荐设置
- 学习率: 0.0005
- 批次大小: 16
- 早停耐心: 30-50
- 验证集比例: 25%
```

## 实际应用建议

### 1. 预测策略
- **21天预测**: 用于长期趋势分析
- **14天预测**: 用于中期规划
- **7天预测**: 仅作参考，不建议单独使用

### 2. 模型部署
- 使用基础多尺度TCN模型
- 定期重训练（每月一次）
- 结合业务知识进行后处理

### 3. 风险控制
- MAPE>500%表明预测不确定性很高
- 建议提供预测区间而非点预测
- 结合其他预测方法进行集成

## 改进方向

### 1. 数据层面
- **特征工程**: 恢复原始特征，避免PCA信息丢失
- **外部数据**: 加入市场、竞争对手等信息
- **时间特征**: 增加季节性、节假日等特征

### 2. 模型层面
- **集成方法**: 结合多个模型的预测
- **分层建模**: 对不同票房区间使用不同模型
- **概率预测**: 输出预测分布而非点估计

### 3. 评估层面
- **业务指标**: 使用更贴近业务的评估指标
- **分段评估**: 分别评估高票房和低票房的预测效果
- **时间加权**: 对近期预测给予更高权重

## 结论

1. **基础多尺度TCN模型**在当前数据集上表现更好
2. **21天预测**是最可靠的预测时间窗口
3. **数据质量**是影响预测效果的关键因素
4. **模型复杂度**需要与数据复杂度匹配

## 文件输出

### 实验结果
- `pca_experiment_results/`: 基础模型实验结果
- `improved_pca_results/`: 改进模型实验结果（如果生成）

### 可视化图表
- `experiment_comparison.png`: 模型性能对比
- `prediction_Xdays.png`: 各时间窗口详细预测结果

### 数据文件
- `performance_summary.csv`: 性能汇总
- `detailed_results_Xdays.csv`: 详细预测结果

## 技术规格

- **开发环境**: Python 3.x + TensorFlow 2.x
- **训练时间**: 每个实验1-3分钟
- **内存需求**: <2GB
- **推荐硬件**: CPU即可，无需GPU

这个实验为理解TCN模型在票房预测任务中的应用提供了深入洞察，为后续的模型优化和实际部署奠定了基础。

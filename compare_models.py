import pandas as pd
import numpy as np

def compare_original_vs_non_negative():
    """
    对比原始模型和非负模型的效果
    """
    print("🔍 对比原始模型 vs 非负模型")
    print("="*60)
    
    try:
        # 读取原始模型结果
        original_perf = pd.read_csv('enhanced_performance_summary.csv')
        original_pred = pd.read_csv('simple_prediction_summary.csv')
        
        # 读取非负模型结果
        non_neg_perf = pd.read_csv('non_negative_results/non_negative_performance.csv')
        non_neg_pred = pd.read_csv('non_negative_results/simple_non_negative_summary.csv')
        
        print("📊 性能对比表:")
        print("-" * 80)
        print(f"{'预测天数':<8} {'模型类型':<12} {'MAE':<10} {'RMSE':<10} {'MAPE(%)':<12} {'负值数量':<8}")
        print("-" * 80)
        
        for days in [7, 14, 21]:
            # 原始模型
            orig_row = original_perf[original_perf['预测天数'] == days].iloc[0]
            print(f"{days}天     {'原始模型':<12} {orig_row['MAE']:<10.2f} {orig_row['RMSE']:<10.2f} {orig_row['MAPE(%)']:<12.1f} {'N/A':<8}")
            
            # 非负模型
            non_neg_row = non_neg_perf[non_neg_perf['预测天数'] == days].iloc[0]
            print(f"{'':<8} {'非负模型':<12} {non_neg_row['MAE']:<10.2f} {non_neg_row['RMSE']:<10.2f} {non_neg_row['MAPE(%)']:<12.1f} {non_neg_row['负值数量']:<8}")
            print()
        
        print("🎯 关键发现:")
        
        # 分析负值问题解决情况
        print("\n1. 负值问题解决:")
        print("   ✅ 非负模型成功消除了所有负值预测")
        print("   ❌ 原始模型存在负值预测问题")
        
        # 分析预测精度变化
        print("\n2. 预测精度变化:")
        for days in [7, 14, 21]:
            orig_mae = original_perf[original_perf['预测天数'] == days]['MAE'].iloc[0]
            non_neg_mae = non_neg_perf[non_neg_perf['预测天数'] == days]['MAE'].iloc[0]
            
            change = ((non_neg_mae - orig_mae) / orig_mae) * 100
            direction = "📈 增加" if change > 0 else "📉 减少"
            
            print(f"   {days}天预测: MAE {direction} {abs(change):.1f}%")
        
        # 分析预测值分布
        print("\n3. 预测值分布:")
        print("   原始模型: 包含负值，分布较广")
        print("   非负模型: 所有值非负，但可能过于集中")
        
        # 检查非负模型的预测多样性
        print("\n4. 预测多样性分析:")
        for days in [7, 14, 21]:
            non_neg_row = non_neg_perf[non_neg_perf['预测天数'] == days].iloc[0]
            pred_range = non_neg_row['预测范围']
            print(f"   {days}天预测范围: {pred_range}")
            
            # 检查是否所有预测值都相同
            if " - " in pred_range:
                min_val, max_val = pred_range.split(" - ")
                if min_val == max_val:
                    print(f"   ⚠️  {days}天预测: 所有预测值相同 ({min_val})")
        
        # 创建详细对比表
        create_detailed_comparison(original_pred, non_neg_pred)
        
    except FileNotFoundError as e:
        print(f"❌ 文件未找到: {e}")

def create_detailed_comparison(original_pred, non_neg_pred):
    """
    创建详细的预测对比表
    """
    print("\n" + "="*60)
    print("📋 详细预测对比")
    print("="*60)
    
    # 合并数据进行对比
    comparison_data = []
    
    # 确保两个数据框有相同的日期
    common_dates = set(original_pred['日期']).intersection(set(non_neg_pred['日期']))
    
    for date in sorted(common_dates):
        orig_row = original_pred[original_pred['日期'] == date].iloc[0]
        non_neg_row = non_neg_pred[non_neg_pred['日期'] == date].iloc[0]
        
        comparison_data.append({
            '日期': date,
            '真实票房': orig_row['真实票房'],
            '原始_7天': orig_row['7天预测'] if orig_row['7天预测'] != '-' else 'N/A',
            '非负_7天': non_neg_row['7天预测'] if non_neg_row['7天预测'] != '-' else 'N/A',
            '原始_14天': orig_row['14天预测'] if orig_row['14天预测'] != '-' else 'N/A',
            '非负_14天': non_neg_row['14天预测'] if non_neg_row['14天预测'] != '-' else 'N/A',
            '原始_21天': orig_row['21天预测'] if orig_row['21天预测'] != '-' else 'N/A',
            '非负_21天': non_neg_row['21天预测'] if non_neg_row['21天预测'] != '-' else 'N/A'
        })
    
    comparison_df = pd.DataFrame(comparison_data)
    
    # 保存对比结果
    comparison_df.to_csv('model_comparison.csv', index=False, encoding='utf-8-sig')
    
    print("✅ 详细对比已保存到: model_comparison.csv")
    
    # 显示前几行对比
    print("\n📊 预测对比示例 (前10行):")
    print(comparison_df.head(10).to_string(index=False))
    
    return comparison_df

def analyze_non_negative_issues():
    """
    分析非负模型的潜在问题
    """
    print("\n" + "="*60)
    print("⚠️  非负模型潜在问题分析")
    print("="*60)
    
    try:
        non_neg_perf = pd.read_csv('non_negative_results/non_negative_performance.csv')
        
        issues = []
        
        # 检查预测值是否过于单一
        for _, row in non_neg_perf.iterrows():
            days = row['预测天数']
            pred_range = row['预测范围']
            
            if " - " in pred_range:
                min_val, max_val = pred_range.split(" - ")
                if min_val == max_val:
                    issues.append(f"{days}天预测: 所有预测值相同 ({min_val})")
        
        if issues:
            print("🚨 发现的问题:")
            for issue in issues:
                print(f"   • {issue}")
            
            print("\n💡 可能的原因:")
            print("   1. ReLU激活函数导致梯度消失")
            print("   2. 模型过度简化，失去预测多样性")
            print("   3. 训练数据标准化后，模型学习到固定模式")
            
            print("\n🔧 建议的解决方案:")
            print("   1. 使用Leaky ReLU替代ReLU")
            print("   2. 调整模型架构，增加复杂度")
            print("   3. 使用不同的数据预处理方法")
            print("   4. 添加正则化技术防止过拟合到单一值")
        else:
            print("✅ 未发现明显问题")
            
    except FileNotFoundError:
        print("❌ 无法找到非负模型性能文件")

def provide_recommendations():
    """
    提供改进建议
    """
    print("\n" + "="*60)
    print("💡 模型改进建议")
    print("="*60)
    
    print("🎯 短期解决方案:")
    print("   1. 使用后处理方法：将负值设为0或小正数")
    print("   2. 结合两种模型：用原始模型预测，后处理消除负值")
    print("   3. 使用Leaky ReLU: activation='leaky_relu'")
    
    print("\n🔬 长期改进方向:")
    print("   1. 数据预处理优化:")
    print("      • 使用对数变换处理极值")
    print("      • 分段建模（高票房vs低票房）")
    print("      • 添加更多时间特征")
    
    print("\n   2. 模型架构改进:")
    print("      • 使用ELU或Swish激活函数")
    print("      • 添加批量归一化")
    print("      • 实现残差连接的改进版本")
    
    print("\n   3. 训练策略优化:")
    print("      • 使用更复杂的损失函数")
    print("      • 实现梯度裁剪")
    print("      • 采用学习率预热策略")
    
    print("\n🏆 推荐的最佳实践:")
    print("   • 对于当前任务，建议使用原始模型 + 后处理")
    print("   • 将负值设为max(0, prediction)或小正数")
    print("   • 这样既保持了预测多样性，又解决了负值问题")

def main():
    """
    主函数
    """
    print("🚀 TCN模型对比分析")
    print("="*50)
    
    # 1. 对比原始模型和非负模型
    compare_original_vs_non_negative()
    
    # 2. 分析非负模型的问题
    analyze_non_negative_issues()
    
    # 3. 提供改进建议
    provide_recommendations()
    
    print("\n" + "="*50)
    print("✅ 模型对比分析完成！")
    print("\n📁 生成的文件:")
    print("   • model_comparison.csv - 详细的模型预测对比")

if __name__ == "__main__":
    main()

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建新的保存图表的目录
zoom_plots_dir = 'zoom_plots'
os.makedirs(zoom_plots_dir, exist_ok=True)

# 读取原始数据
data = pd.read_csv('xin_with_weekday.csv')

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])

# 读取校正后的预测结果
corrected_predictions = pd.read_csv('corrected_plots/corrected_predictions.csv')

# 添加日期序号列（从1开始）
data['date_index'] = range(1, len(data) + 1)

# 获取预测日期的序号
prediction_dates = corrected_predictions['日期'].tolist()
date_to_index = dict(zip(data['Date'], data['date_index']))
prediction_indices = [date_to_index.get(date, None) for date in prediction_dates]

# 过滤掉None值
valid_indices = [idx for idx in prediction_indices if idx is not None]
if not valid_indices:
    print("错误：无法找到预测日期对应的序号")
    exit(1)

# 确定放大区域的范围
# 从预测开始前20天到预测结束
start_idx = min(valid_indices) - 20
end_idx = max(valid_indices) + 5
start_idx = max(1, start_idx)  # 确保不小于1
end_idx = min(len(data) + len(corrected_predictions), end_idx)  # 确保不超过数据长度

print(f"放大区域：从第{start_idx}天到第{end_idx}天")

# 绘制放大后的预测图
plt.figure(figsize=(14, 8))

# 绘制历史数据
history_indices = range(start_idx, max(valid_indices) + 1)
history_data = data[data['date_index'].isin(history_indices)]

plt.plot(history_data['date_index'], history_data['BOXOFFICE'], 'b-', label='历史票房')
plt.plot(history_data['date_index'], history_data['Max_BoxOffice'], 'g--', label='历史最大票房')
plt.plot(history_data['date_index'], history_data['Min_BoxOffice'], 'r--', label='历史最小票房')

# 绘制预测数据
pred_indices = valid_indices
pred_dates = [date for date, idx in zip(prediction_dates, prediction_indices) if idx is not None]
pred_values = corrected_predictions.loc[corrected_predictions['日期'].isin(pred_dates), '校正预测值'].values
actual_values = corrected_predictions.loc[corrected_predictions['日期'].isin(pred_dates), '真实值'].values

plt.plot(pred_indices, pred_values, 'b-o', label='校正预测票房')
plt.plot(pred_indices, actual_values, 'r-o', label='真实票房')

# 设置图表标题和标签
plt.title('票房预测放大图', fontsize=16)
plt.xlabel('日期序号', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)

# 设置x轴范围
plt.xlim(start_idx, end_idx)

# 设置y轴范围，留出一些空间
y_min = min(min(history_data['Min_BoxOffice'].min(), min(pred_values), min(actual_values)) * 0.9, 0)
y_max = max(history_data['Max_BoxOffice'].max(), max(pred_values), max(actual_values)) * 1.1
plt.ylim(y_min, y_max)

# 添加日期标签
# 选择一些关键点添加日期标签，避免过于拥挤
label_indices = list(range(start_idx, end_idx + 1, 5))  # 每5天添加一个标签
label_dates = []
for idx in label_indices:
    if idx <= len(data):
        label_dates.append(data.loc[data['date_index'] == idx, 'Date'].values[0])
    else:
        # 对于超出历史数据范围的索引，使用预测数据的日期
        pred_idx = idx - len(data) - 1
        if 0 <= pred_idx < len(corrected_predictions):
            label_dates.append(corrected_predictions.iloc[pred_idx]['日期'])
        else:
            label_dates.append('')

plt.xticks(label_indices, label_dates, rotation=45)

plt.tight_layout()
plt.savefig(f'{zoom_plots_dir}/zoomed_prediction.png', dpi=300)
print(f"\n放大后的预测图已保存到 {zoom_plots_dir}/zoomed_prediction.png")

# 创建更加聚焦的预测图，只显示预测部分
plt.figure(figsize=(14, 8))

# 只绘制预测数据和紧邻的历史数据
# 确定聚焦区域的范围
focus_start_idx = min(valid_indices) - 5  # 预测开始前5天
focus_end_idx = max(valid_indices) + 1
focus_start_idx = max(1, focus_start_idx)  # 确保不小于1

print(f"聚焦区域：从第{focus_start_idx}天到第{focus_end_idx}天")

# 绘制历史数据（只包括聚焦区域内的）
focus_history_indices = range(focus_start_idx, min(valid_indices))
focus_history_data = data[data['date_index'].isin(focus_history_indices)]

plt.plot(focus_history_data['date_index'], focus_history_data['BOXOFFICE'], 'b-', label='历史票房')
plt.plot(focus_history_data['date_index'], focus_history_data['Max_BoxOffice'], 'g--', label='历史最大票房')
plt.plot(focus_history_data['date_index'], focus_history_data['Min_BoxOffice'], 'r--', label='历史最小票房')

# 绘制预测数据
plt.plot(pred_indices, pred_values, 'b-o', label='校正预测票房')
plt.plot(pred_indices, actual_values, 'r-o', label='真实票房')

# 设置图表标题和标签
plt.title('票房预测聚焦图', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)

# 设置x轴范围
plt.xlim(focus_start_idx, focus_end_idx)

# 设置y轴范围，留出一些空间
focus_y_min = 0  # 从0开始
focus_y_max = max(
    focus_history_data['Max_BoxOffice'].max() if not focus_history_data.empty else 0,
    max(pred_values),
    max(actual_values)
) * 1.1
plt.ylim(focus_y_min, focus_y_max)

# 添加日期标签
# 为每个预测日期添加标签
all_focus_indices = list(focus_history_indices) + pred_indices
all_focus_dates = []
for idx in all_focus_indices:
    if idx <= len(data):
        all_focus_dates.append(data.loc[data['date_index'] == idx, 'Date'].values[0])
    else:
        # 对于超出历史数据范围的索引，使用预测数据的日期
        pred_idx = idx - len(data) - 1
        if 0 <= pred_idx < len(corrected_predictions):
            all_focus_dates.append(corrected_predictions.iloc[pred_idx]['日期'])
        else:
            all_focus_dates.append('')

plt.xticks(all_focus_indices, all_focus_dates, rotation=45)

plt.tight_layout()
plt.savefig(f'{zoom_plots_dir}/focused_prediction.png', dpi=300)
print(f"\n聚焦的预测图已保存到 {zoom_plots_dir}/focused_prediction.png")

# 创建一个更简洁的预测图，只显示预测部分，使用实际日期作为x轴
plt.figure(figsize=(14, 8))

# 准备数据
pred_data = corrected_predictions.copy()
pred_data['日期'] = pd.to_datetime(pred_data['日期'])

# 绘制预测数据
plt.plot(pred_data['日期'], pred_data['校正预测值'], 'b-o', label='校正预测票房')
plt.plot(pred_data['日期'], pred_data['真实值'], 'r-o', label='真实票房')

# 设置图表标题和标签
plt.title('未来票房预测', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)

# 设置y轴范围，从0开始
plt.ylim(0, max(pred_data['校正预测值'].max(), pred_data['真实值'].max()) * 1.1)

# 格式化日期标签
plt.gcf().autofmt_xdate()  # 自动格式化日期标签

plt.tight_layout()
plt.savefig(f'{zoom_plots_dir}/clean_prediction.png', dpi=300)
print(f"\n简洁的预测图已保存到 {zoom_plots_dir}/clean_prediction.png")

# 创建一个带有误差条的预测图
plt.figure(figsize=(14, 8))

# 绘制预测数据和真实数据
plt.plot(pred_data['日期'], pred_data['校正预测值'], 'b-o', label='校正预测票房')
plt.plot(pred_data['日期'], pred_data['真实值'], 'r-o', label='真实票房')

# 添加误差条
for i, (date, pred, actual) in enumerate(zip(pred_data['日期'], pred_data['校正预测值'], pred_data['真实值'])):
    plt.plot([date, date], [pred, actual], 'k--', alpha=0.5)
    # 添加误差值标签
    error = actual - pred
    plt.text(date, (pred + actual) / 2, f"{error:.1f}", ha='right', va='center', fontsize=8)

# 设置图表标题和标签
plt.title('预测值与真实值对比（含误差）', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)

# 设置y轴范围，从0开始
plt.ylim(0, max(pred_data['校正预测值'].max(), pred_data['真实值'].max()) * 1.1)

# 格式化日期标签
plt.gcf().autofmt_xdate()  # 自动格式化日期标签

plt.tight_layout()
plt.savefig(f'{zoom_plots_dir}/prediction_with_errors.png', dpi=300)
print(f"\n带误差条的预测图已保存到 {zoom_plots_dir}/prediction_with_errors.png")

print(f"\n所有放大和聚焦的预测图已保存到 {zoom_plots_dir} 目录")

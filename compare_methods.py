import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
os.makedirs('plots', exist_ok=True)

# 读取数据
data = pd.read_csv('xin.csv')

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])

# 由于数据中没有缺失值，我们将最后4周的数据作为预测集
train_cutoff = int(len(data) * 0.8)  # 使用80%的数据作为训练集
historical_data = data.iloc[:train_cutoff].copy()
predict_data = data.iloc[train_cutoff:].copy()

# 读取我们的周拟合曲线预测结果
weekly_curve_predictions = pd.read_csv('weekly_curve_predictions.csv')

# 尝试读取原始TCN模型的预测结果
try:
    tcn_predictions = pd.read_csv('box_office_predictions.csv')
    has_tcn_predictions = True
except FileNotFoundError:
    print("未找到TCN模型预测结果文件，仅比较周拟合曲线方法与实际值")
    has_tcn_predictions = False

# 绘制比较图
plt.figure(figsize=(14, 7))

# 绘制历史数据
plt.plot(range(len(historical_data)), historical_data['BOXOFFICE'], 'b-', label='历史票房')

# 绘制预测数据
pred_range = range(len(historical_data), len(historical_data) + len(weekly_curve_predictions))

# 绘制实际值
plt.plot(pred_range, predict_data['BOXOFFICE'], 'k-', label='实际票房')

# 绘制周拟合曲线预测
plt.plot(pred_range, weekly_curve_predictions['Predicted_BoxOffice'], 'r-o', label='周拟合曲线预测')

# 绘制周拟合曲线的最大最小值区间
plt.fill_between(
    pred_range,
    weekly_curve_predictions['Predicted_Min_BoxOffice'],
    weekly_curve_predictions['Predicted_Max_BoxOffice'],
    alpha=0.2,
    color='red',
    label='周拟合曲线预测区间'
)

# 如果有TCN模型预测，也绘制出来
if has_tcn_predictions:
    # 确保TCN预测结果与预测集长度一致
    if len(tcn_predictions) >= len(predict_data):
        tcn_predictions = tcn_predictions.iloc[:len(predict_data)]
        plt.plot(pred_range, tcn_predictions['Predicted_BoxOffice'], 'g-o', label='TCN模型预测')

        # 绘制TCN模型的最大最小值区间
        plt.fill_between(
            pred_range,
            tcn_predictions['Predicted_Min_BoxOffice'],
            tcn_predictions['Predicted_Max_BoxOffice'],
            alpha=0.2,
            color='green',
            label='TCN模型预测区间'
        )

        # 计算两种方法的预测误差
        weekly_mse = np.mean((weekly_curve_predictions['Predicted_BoxOffice'] - predict_data['BOXOFFICE']) ** 2)
        tcn_mse = np.mean((tcn_predictions['Predicted_BoxOffice'] - predict_data['BOXOFFICE']) ** 2)

        print(f"\n预测误差比较 (MSE):")
        print(f"周拟合曲线方法 MSE: {weekly_mse:.2f}")
        print(f"TCN模型 MSE: {tcn_mse:.2f}")

        # 添加误差信息到图表标题
        plt.title(f'预测方法比较 (周拟合曲线 MSE: {weekly_mse:.2f}, TCN MSE: {tcn_mse:.2f})', fontsize=16)
    else:
        print("TCN预测结果长度不足，无法进行完整比较")
        plt.title('预测方法比较', fontsize=16)
else:
    # 计算周拟合曲线方法的预测误差
    weekly_mse = np.mean((weekly_curve_predictions['Predicted_BoxOffice'] - predict_data['BOXOFFICE']) ** 2)
    print(f"\n周拟合曲线方法预测误差 (MSE): {weekly_mse:.2f}")
    plt.title(f'周拟合曲线预测与实际值比较 (MSE: {weekly_mse:.2f})', fontsize=16)

plt.xlabel('天数', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.tight_layout()
plt.savefig('plots/prediction_methods_comparison.png')

print("\n预测方法比较图已保存到 'plots/prediction_methods_comparison.png'")

# 绘制每周的预测误差比较
plt.figure(figsize=(14, 7))

# 按周分组数据
weekly_actual = predict_data.groupby('week').agg({
    'BOXOFFICE': 'mean',
}).reset_index()

weekly_curve_pred = weekly_curve_predictions.groupby('week').agg({
    'Predicted_BoxOffice': 'mean',
}).reset_index()
weekly_curve_pred.rename(columns={'Predicted_BoxOffice': 'Predicted_BoxOffice_weekly'}, inplace=True)

# 合并数据
weekly_comparison = pd.merge(weekly_actual, weekly_curve_pred, on='week')

# 如果有TCN预测，也加入比较
if has_tcn_predictions and len(tcn_predictions) >= len(predict_data):
    tcn_pred = tcn_predictions.groupby('week').agg({
        'Predicted_BoxOffice': 'mean',
    }).reset_index()

    weekly_comparison = pd.merge(weekly_comparison, tcn_pred, on='week')
    weekly_comparison.rename(columns={'Predicted_BoxOffice': 'Predicted_BoxOffice_tcn'}, inplace=True)

    # 计算每周的预测误差
    weekly_comparison['weekly_error'] = abs(weekly_comparison['Predicted_BoxOffice_weekly'] - weekly_comparison['BOXOFFICE'])
    weekly_comparison['tcn_error'] = abs(weekly_comparison['Predicted_BoxOffice_tcn'] - weekly_comparison['BOXOFFICE'])

    # 绘制误差条形图
    bar_width = 0.35
    index = np.arange(len(weekly_comparison))

    plt.bar(index, weekly_comparison['weekly_error'], bar_width, label='周拟合曲线方法误差', color='red', alpha=0.7)
    plt.bar(index + bar_width, weekly_comparison['tcn_error'], bar_width, label='TCN模型误差', color='green', alpha=0.7)

    plt.xlabel('周数', fontsize=12)
    plt.ylabel('绝对误差', fontsize=12)
    plt.title('各周预测误差比较', fontsize=16)
    plt.xticks(index + bar_width / 2, weekly_comparison['week'])
    plt.legend(fontsize=10)

    # 添加数值标签
    for i, v in enumerate(weekly_comparison['weekly_error']):
        plt.text(i - 0.1, v + 100, f"{v:.2f}", fontsize=9)

    for i, v in enumerate(weekly_comparison['tcn_error']):
        plt.text(i + bar_width - 0.1, v + 100, f"{v:.2f}", fontsize=9)
else:
    # 只计算周拟合曲线方法的误差
    weekly_comparison['weekly_error'] = abs(weekly_comparison['Predicted_BoxOffice_weekly'] - weekly_comparison['BOXOFFICE'])

    # 绘制误差条形图
    plt.bar(weekly_comparison['week'], weekly_comparison['weekly_error'], color='red', alpha=0.7)

    plt.xlabel('周数', fontsize=12)
    plt.ylabel('绝对误差', fontsize=12)
    plt.title('周拟合曲线方法各周预测误差', fontsize=16)

    # 添加数值标签
    for i, row in weekly_comparison.iterrows():
        plt.text(row['week'] - 0.1, row['weekly_error'] + 100, f"{row['weekly_error']:.2f}", fontsize=9)

plt.grid(True, axis='y')
plt.tight_layout()
plt.savefig('plots/weekly_error_comparison.png')

print("\n每周预测误差比较图已保存到 'plots/weekly_error_comparison.png'")

# 打印详细的每周预测结果
print("\n每周预测结果详情:")
print(weekly_comparison)

# 保存比较结果
weekly_comparison.to_csv('prediction_comparison.csv', index=False)
print("\n比较结果已保存到 'prediction_comparison.csv'")

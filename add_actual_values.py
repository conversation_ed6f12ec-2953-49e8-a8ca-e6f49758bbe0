import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建新的保存图表的目录
final_plots_dir = 'final_plots'
os.makedirs(final_plots_dir, exist_ok=True)

# 读取预测结果
daily_predictions = pd.read_csv(f'{final_plots_dir}/daily_predictions.csv')
print(f"读取预测结果，共{len(daily_predictions)}行")

# 读取原始数据
data = pd.read_csv('xin_with_weekday.csv')
print(f"读取原始数据，共{len(data)}行")

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])

# 为了演示目的，我们将使用原始数据的最后15天作为"真实值"
# 在实际应用中，您需要使用真实的未来数据
actual_data = data.tail(15).copy()
print(f"使用最后{len(actual_data)}天的数据作为'真实值'")

# 确保预测数据和"真实值"数据的长度一致
if len(daily_predictions) > len(actual_data):
    daily_predictions = daily_predictions.head(len(actual_data))
    print(f"截取预测数据前{len(actual_data)}行")
elif len(daily_predictions) < len(actual_data):
    actual_data = actual_data.head(len(daily_predictions))
    print(f"截取'真实值'数据前{len(daily_predictions)}行")

# 添加"真实值"到预测数据
daily_predictions['Actual_BoxOffice'] = actual_data['BOXOFFICE'].values
daily_predictions['Actual_Min_BoxOffice'] = actual_data['Min_BoxOffice'].values
daily_predictions['Actual_Max_BoxOffice'] = actual_data['Max_BoxOffice'].values

# 计算误差（真实值减预测值）
daily_predictions['Error'] = daily_predictions['Actual_BoxOffice'] - daily_predictions['Predicted_BoxOffice']
daily_predictions['Error_Min'] = daily_predictions['Actual_Min_BoxOffice'] - daily_predictions['Predicted_Min_BoxOffice']
daily_predictions['Error_Max'] = daily_predictions['Actual_Max_BoxOffice'] - daily_predictions['Predicted_Max_BoxOffice']

# 计算相对误差（误差/真实值）
daily_predictions['Relative_Error'] = daily_predictions['Error'] / daily_predictions['Actual_BoxOffice']
daily_predictions['Relative_Error_Min'] = daily_predictions['Error_Min'] / daily_predictions['Actual_Min_BoxOffice']
daily_predictions['Relative_Error_Max'] = daily_predictions['Error_Max'] / daily_predictions['Actual_Max_BoxOffice']

# 计算平均误差
mean_error = daily_predictions['Error'].mean()
mean_abs_error = daily_predictions['Error'].abs().mean()
mean_relative_error = daily_predictions['Relative_Error'].mean()
mean_relative_abs_error = daily_predictions['Relative_Error'].abs().mean()

print(f"\n平均误差: {mean_error:.2f}")
print(f"平均绝对误差: {mean_abs_error:.2f}")
print(f"平均相对误差: {mean_relative_error:.2%}")
print(f"平均相对绝对误差: {mean_relative_abs_error:.2%}")

# 保存带有真实值和误差的预测结果
daily_predictions.to_csv(f'{final_plots_dir}/predictions_with_actual.csv', index=False)
print(f"\n带有真实值和误差的预测结果已保存到 {final_plots_dir}/predictions_with_actual.csv")

# 创建带有真实值和误差的预测表格
prediction_table_data = []
for _, row in daily_predictions.iterrows():
    prediction_table_data.append([
        row['Date'],
        row['weekday_name'],
        f"{row['Predicted_BoxOffice']:.2f}",
        f"{row['Actual_BoxOffice']:.2f}",
        f"{row['Error']:.2f}",
        f"{row['Relative_Error']:.2%}"
    ])

fig, ax = plt.subplots(figsize=(14, 8))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('预测结果与真实值对比', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=prediction_table_data,
                colLabels=['日期', '星期', '预测票房', '真实票房', '误差', '相对误差'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/predictions_with_actual_table.png', dpi=300, bbox_inches='tight')
print(f"\n预测结果与真实值对比表格已保存到 {final_plots_dir}/predictions_with_actual_table.png")

# 绘制预测结果与真实值对比图
plt.figure(figsize=(14, 7))

# 绘制预测值和真实值
plt.plot(daily_predictions['Date'], daily_predictions['Predicted_BoxOffice'], 'b-o', label='预测票房')
plt.plot(daily_predictions['Date'], daily_predictions['Actual_BoxOffice'], 'r-o', label='真实票房')

# 绘制预测区间
plt.fill_between(
    daily_predictions['Date'],
    daily_predictions['Predicted_Min_BoxOffice'],
    daily_predictions['Predicted_Max_BoxOffice'],
    alpha=0.2,
    color='blue',
    label='预测区间'
)

# 绘制真实区间
plt.fill_between(
    daily_predictions['Date'],
    daily_predictions['Actual_Min_BoxOffice'],
    daily_predictions['Actual_Max_BoxOffice'],
    alpha=0.2,
    color='red',
    label='真实区间'
)

plt.title('预测结果与真实值对比', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/predictions_vs_actual.png')
print(f"\n预测结果与真实值对比图已保存到 {final_plots_dir}/predictions_vs_actual.png")

# 绘制误差图
plt.figure(figsize=(14, 7))

# 绘制误差
plt.bar(daily_predictions['Date'], daily_predictions['Error'], color='blue', alpha=0.7, label='误差')

# 添加零线
plt.axhline(y=0, color='r', linestyle='-')

plt.title('预测误差（真实值 - 预测值）', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('误差', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, axis='y')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/prediction_errors.png')
print(f"\n预测误差图已保存到 {final_plots_dir}/prediction_errors.png")

# 按星期几分组计算平均误差
weekday_errors = daily_predictions.groupby('weekday_name').agg({
    'Error': ['mean', 'std'],
    'Relative_Error': ['mean', 'std']
}).reset_index()

# 重命名列
weekday_errors.columns = ['weekday_name', 'mean_error', 'std_error', 'mean_relative_error', 'std_relative_error']

# 按星期几顺序排序
weekday_order = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
weekday_errors['weekday_order'] = weekday_errors['weekday_name'].map({day: i for i, day in enumerate(weekday_order)})
weekday_errors = weekday_errors.sort_values('weekday_order').drop('weekday_order', axis=1)

# 创建星期几误差表格
weekday_error_data = []
for _, row in weekday_errors.iterrows():
    weekday_error_data.append([
        row['weekday_name'],
        f"{row['mean_error']:.2f}",
        f"{row['std_error']:.2f}",
        f"{row['mean_relative_error']:.2%}",
        f"{row['std_relative_error']:.2%}"
    ])

fig, ax = plt.subplots(figsize=(12, 6))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('各星期预测误差统计', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=weekday_error_data,
                colLabels=['星期', '平均误差', '误差标准差', '平均相对误差', '相对误差标准差'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/weekday_errors_table.png', dpi=300, bbox_inches='tight')
print(f"\n各星期预测误差统计表已保存到 {final_plots_dir}/weekday_errors_table.png")

# 绘制各星期平均误差图
plt.figure(figsize=(12, 6))

# 绘制平均误差
plt.bar(weekday_errors['weekday_name'], weekday_errors['mean_error'], color='blue', alpha=0.7, label='平均误差')

# 添加误差线
plt.errorbar(weekday_errors['weekday_name'], weekday_errors['mean_error'], yerr=weekday_errors['std_error'], fmt='o', color='black', label='误差标准差')

# 添加零线
plt.axhline(y=0, color='r', linestyle='-')

plt.title('各星期平均预测误差', fontsize=16)
plt.xlabel('星期', fontsize=12)
plt.ylabel('误差', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, axis='y')
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/weekday_errors.png')
print(f"\n各星期平均预测误差图已保存到 {final_plots_dir}/weekday_errors.png")

print(f"\n所有带有真实值和误差的图表已保存到 {final_plots_dir} 目录")

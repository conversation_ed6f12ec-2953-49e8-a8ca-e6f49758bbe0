import pandas as pd
import numpy as np
import shutil
from datetime import datetime

def replace_with_improved_predictions():
    """
    用改进后的预测结果替换原来的CSV文件
    """
    print("🔄 替换原始预测结果为改进版本")
    print("="*60)
    
    try:
        # 1. 备份原始文件
        print("📦 备份原始文件...")
        backup_files = [
            'simple_prediction_summary.csv',
            'corrected_prediction_summary.csv',
            'consolidated_prediction_results.csv'
        ]
        
        for file in backup_files:
            try:
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file}"
                shutil.copy2(file, backup_name)
                print(f"  ✅ {file} → {backup_name}")
            except FileNotFoundError:
                print(f"  ⚠️  {file} 不存在，跳过备份")
        
        # 2. 读取改进后的预测结果
        print(f"\n📊 读取改进后的预测结果...")
        
        # 读取各种改进结果
        improved_7day = pd.read_csv('final_improved_7day_predictions.csv')
        original_corrected = pd.read_csv('corrected_prediction_summary.csv')
        
        print(f"  ✅ 改进的7天预测: {len(improved_7day)}行")
        print(f"  ✅ 原始修正结果: {len(original_corrected)}行")
        
        # 3. 创建新的综合预测结果
        print(f"\n🔧 创建新的综合预测结果...")
        
        # 以原始修正结果为基础
        new_results = original_corrected.copy()
        
        # 替换7天预测结果
        for _, improved_row in improved_7day.iterrows():
            date = improved_row['日期']
            improved_pred = improved_row['推荐预测']
            improved_error = improved_row['预测误差']
            
            # 在新结果中找到对应日期并更新
            date_mask = new_results['日期'] == date
            if date_mask.any():
                new_results.loc[date_mask, '7天预测'] = f"{improved_pred:.1f}"
                new_results.loc[date_mask, '7天误差'] = f"{improved_error:.1f}"
                print(f"  🔄 更新 {date}: 7天预测 {improved_pred:.1f}")
        
        # 4. 重新计算相对误差
        print(f"\n📈 重新计算相对误差...")
        
        for idx, row in new_results.iterrows():
            actual = row['真实票房']
            
            # 重新计算7天预测的相对误差
            if row['7天预测'] != '-':
                try:
                    pred_val = float(row['7天预测'])
                    relative_error = abs((actual - pred_val) / actual) * 100
                    new_results.loc[idx, '7天相对误差(%)'] = f"{relative_error:.1f}"
                except:
                    pass
        
        # 5. 保存新的预测结果文件
        print(f"\n💾 保存改进后的预测结果...")
        
        # 替换主要文件
        new_results.to_csv('corrected_prediction_summary.csv', index=False, encoding='utf-8-sig')
        print(f"  ✅ 已更新: corrected_prediction_summary.csv")
        
        # 创建新的简化版本
        create_new_simple_summary(new_results)
        
        # 创建新的整合版本
        create_new_consolidated_results(new_results)
        
        # 6. 显示改进效果对比
        show_improvement_comparison(improved_7day, original_corrected)
        
        print(f"\n" + "="*60)
        print("✅ 预测结果替换完成！")
        print(f"\n📁 更新的文件:")
        print(f"  • corrected_prediction_summary.csv - 主要预测结果 ⭐")
        print(f"  • simple_prediction_summary.csv - 简化版本")
        print(f"  • consolidated_prediction_results.csv - 完整整合版本")
        
        print(f"\n📦 备份文件:")
        print(f"  • backup_*_*.csv - 原始文件备份")
        
        print(f"\n💡 主要改进:")
        print(f"  • 7天预测MAE从483.73改进到55.07 (改进88.6%)")
        print(f"  • 7天预测MAPE从1410.9%改进到180.1% (改进87.2%)")
        print(f"  • 预测结果更加合理和稳定")
        
    except Exception as e:
        print(f"❌ 替换过程中出现错误: {e}")

def create_new_simple_summary(new_results):
    """
    创建新的简化汇总
    """
    print(f"  📋 创建新的简化汇总...")
    
    # 筛选有预测值的行
    prediction_mask = (
        (new_results['7天预测'] != "-") | 
        (new_results['14天预测'] != "-") | 
        (new_results['21天预测'] != "-")
    )
    
    simple_data = []
    for _, row in new_results[prediction_mask].iterrows():
        simple_data.append({
            '日期': row['日期'],
            '真实票房': f"{row['真实票房']:.1f}",
            '7天预测': row['7天预测'] if row['7天预测'] != '-' else "-",
            '14天预测': row['14天预测'] if row['14天预测'] != '-' else "-",
            '21天预测': row['21天预测'] if row['21天预测'] != '-' else "-",
            '7天误差': row['7天误差'] if row['7天误差'] != '-' else "-",
            '14天误差': row['14天误差'] if row['14天误差'] != '-' else "-",
            '21天误差': row['21天误差'] if row['21天误差'] != '-' else "-"
        })
    
    simple_df = pd.DataFrame(simple_data)
    simple_df.to_csv('simple_prediction_summary.csv', index=False, encoding='utf-8-sig')
    print(f"    ✅ simple_prediction_summary.csv")

def create_new_consolidated_results(new_results):
    """
    创建新的整合结果
    """
    print(f"  📊 创建新的整合结果...")
    
    # 读取原始数据
    original_data = pd.read_csv('PCA4_NeZha.csv')
    
    # 创建完整的整合结果
    consolidated = original_data[['ds', 'y']].copy()
    consolidated.rename(columns={'y': '真实票房'}, inplace=True)
    
    # 添加预测结果列
    for col in ['7天预测', '14天预测', '21天预测', '7天预测误差', '14天预测误差', '21天预测误差']:
        consolidated[col] = np.nan
    
    # 填入预测结果
    for _, row in new_results.iterrows():
        date_match = consolidated['ds'] == row['日期']
        if date_match.any():
            idx = consolidated[date_match].index[0]
            
            if row['7天预测'] != '-':
                consolidated.loc[idx, '7天预测'] = float(row['7天预测'])
                consolidated.loc[idx, '7天预测误差'] = float(row['7天误差'])
            
            if row['14天预测'] != '-':
                consolidated.loc[idx, '14天预测'] = float(row['14天预测'])
                consolidated.loc[idx, '14天预测误差'] = float(row['14天误差'])
            
            if row['21天预测'] != '-':
                consolidated.loc[idx, '21天预测'] = float(row['21天预测'])
                consolidated.loc[idx, '21天预测误差'] = float(row['21天误差'])
    
    # 添加数据类型标识
    consolidated['数据类型'] = '历史数据'
    has_pred = (~consolidated['7天预测'].isna()) | (~consolidated['14天预测'].isna()) | (~consolidated['21天预测'].isna())
    consolidated.loc[has_pred, '数据类型'] = '预测期'
    
    consolidated.to_csv('consolidated_prediction_results.csv', index=False, encoding='utf-8-sig')
    print(f"    ✅ consolidated_prediction_results.csv")

def show_improvement_comparison(improved_7day, original_corrected):
    """
    显示改进效果对比
    """
    print(f"\n📈 改进效果对比:")
    print("-" * 50)
    
    # 计算原始7天预测的性能
    original_7day_data = []
    for _, row in original_corrected.iterrows():
        if row['7天预测'] != '-':
            original_7day_data.append({
                'actual': row['真实票房'],
                'prediction': float(row['7天预测']),
                'error': float(row['7天误差'])
            })
    
    if original_7day_data:
        original_df = pd.DataFrame(original_7day_data)
        original_mae = original_df['error'].mean()
        original_mape = np.mean(original_df['error'] / original_df['actual']) * 100
        
        # 改进后的性能
        improved_mae = improved_7day['预测误差'].mean()
        improved_mape = np.mean(improved_7day['预测误差'] / improved_7day['真实票房']) * 100
        
        print(f"7天预测改进效果:")
        print(f"  原始MAE: {original_mae:.2f}")
        print(f"  改进MAE: {improved_mae:.2f}")
        print(f"  MAE改进: {((original_mae - improved_mae) / original_mae * 100):.1f}%")
        print(f"  原始MAPE: {original_mape:.1f}%")
        print(f"  改进MAPE: {improved_mape:.1f}%")
        print(f"  MAPE改进: {((original_mape - improved_mape) / original_mape * 100):.1f}%")

def verify_replacement():
    """
    验证替换结果
    """
    print(f"\n🔍 验证替换结果...")
    
    try:
        # 读取更新后的文件
        updated_simple = pd.read_csv('simple_prediction_summary.csv')
        updated_corrected = pd.read_csv('corrected_prediction_summary.csv')
        
        print(f"  ✅ simple_prediction_summary.csv: {len(updated_simple)}行")
        print(f"  ✅ corrected_prediction_summary.csv: {len(updated_corrected)}行")
        
        # 显示7天预测的新结果
        print(f"\n📊 更新后的7天预测结果:")
        seven_day_mask = updated_simple['7天预测'] != '-'
        if seven_day_mask.any():
            seven_day_data = updated_simple[seven_day_mask]
            print(seven_day_data[['日期', '真实票房', '7天预测', '7天误差']].to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"  ❌ 验证失败: {e}")
        return False

def main():
    """
    主函数
    """
    print("🚀 替换预测结果为改进版本")
    print("="*50)
    
    # 执行替换
    replace_with_improved_predictions()
    
    # 验证结果
    if verify_replacement():
        print(f"\n🎉 替换成功完成！")
        print(f"\n💡 现在您可以使用以下改进后的文件:")
        print(f"  📊 simple_prediction_summary.csv - 日常使用推荐")
        print(f"  📋 corrected_prediction_summary.csv - 详细分析")
        print(f"  📈 consolidated_prediction_results.csv - 完整数据")
    else:
        print(f"\n⚠️  替换过程中可能存在问题，请检查文件")

if __name__ == "__main__":
    main()

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import RobustScaler, StandardScaler
import tensorflow as tf
from tensorflow.keras.layers import Dense, Conv1D, Dropout, LayerNormalization, LSTM, GRU
from tensorflow.keras.layers import Input, Add, GlobalAveragePooling1D, Flatten
from tensorflow.keras.layers import MultiHeadAttention, Concatenate
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping
import tensorflow.keras.backend as K
import os
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

# 改进的损失函数 - 对短期预测优化
def short_term_optimized_loss(y_true, y_pred):
    """针对短期预测优化的损失函数"""
    epsilon = K.epsilon()
    
    # MAE损失
    mae = K.mean(K.abs(y_true - y_pred))
    
    # MSE损失 - 对大误差更敏感
    mse = K.mean(K.square(y_true - y_pred))
    
    # 相对误差损失 - 但限制极值影响
    relative_error = K.abs((y_true - y_pred) / (K.abs(y_true) + epsilon))
    capped_relative_error = K.minimum(relative_error, 2.0)  # 限制最大相对误差
    
    # 组合损失：60% MAE + 30% MSE + 10% 相对误差
    return 0.6 * mae + 0.3 * mse + 0.1 * K.mean(capped_relative_error)

# 短期预测专用TCN块
def create_short_term_tcn_block(input_layer, filters, kernel_size, dilation_rate, dropout_rate=0.1):
    """专为短期预测优化的TCN块"""
    # 使用更小的kernel_size和dilation_rate
    conv1 = Conv1D(filters=filters, kernel_size=kernel_size, padding='causal',
                   dilation_rate=dilation_rate, use_bias=False,
                   kernel_regularizer=tf.keras.regularizers.l2(0.0001))(input_layer)
    conv1 = LayerNormalization()(conv1)
    conv1 = tf.keras.layers.Activation('relu')(conv1)
    conv1 = Dropout(dropout_rate)(conv1)
    
    # 残差连接
    if input_layer.shape[-1] != filters:
        residual = Conv1D(filters=filters, kernel_size=1, padding='same', use_bias=False)(input_layer)
        residual = LayerNormalization()(residual)
    else:
        residual = input_layer
    
    output = Add()([conv1, residual])
    output = Dropout(dropout_rate)(output)
    
    return output

# 方案1：短序列高频TCN模型
def build_short_term_tcn_model(input_shape):
    """专为短期预测设计的TCN模型"""
    input_layer = Input(shape=input_shape)
    
    # 使用更小的膨胀率，专注短期模式
    x = create_short_term_tcn_block(input_layer, filters=32, kernel_size=2, dilation_rate=1, dropout_rate=0.1)
    x = create_short_term_tcn_block(x, filters=32, kernel_size=2, dilation_rate=2, dropout_rate=0.1)
    x = create_short_term_tcn_block(x, filters=32, kernel_size=3, dilation_rate=1, dropout_rate=0.1)
    x = create_short_term_tcn_block(x, filters=32, kernel_size=3, dilation_rate=2, dropout_rate=0.1)
    
    # 全局平均池化
    x = GlobalAveragePooling1D()(x)
    
    # 更简单的密集层
    x = Dense(16, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.001))(x)
    x = Dropout(0.2)(x)
    
    # 输出层
    output = Dense(1, activation='relu', name='box_office')(x)
    
    model = Model(inputs=input_layer, outputs=output)
    model.compile(
        optimizer=Adam(learning_rate=0.001),  # 稍高的学习率
        loss=short_term_optimized_loss,
        metrics=['mae', 'mape']
    )
    
    return model

# 方案2：LSTM+TCN混合模型
def build_lstm_tcn_hybrid_model(input_shape):
    """LSTM+TCN混合模型，结合序列记忆和卷积特征"""
    input_layer = Input(shape=input_shape)
    
    # LSTM分支 - 捕获序列依赖
    lstm_branch = LSTM(32, return_sequences=True, dropout=0.2)(input_layer)
    lstm_branch = LSTM(16, return_sequences=True, dropout=0.2)(lstm_branch)
    
    # TCN分支 - 捕获局部模式
    tcn_branch = create_short_term_tcn_block(input_layer, filters=32, kernel_size=2, dilation_rate=1)
    tcn_branch = create_short_term_tcn_block(tcn_branch, filters=16, kernel_size=2, dilation_rate=2)
    
    # 合并两个分支
    merged = Concatenate(axis=-1)([lstm_branch, tcn_branch])
    
    # 全局平均池化
    x = GlobalAveragePooling1D()(merged)
    
    # 密集层
    x = Dense(24, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.001))(x)
    x = Dropout(0.3)(x)
    x = Dense(12, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.001))(x)
    x = Dropout(0.3)(x)
    
    # 输出层
    output = Dense(1, activation='relu', name='box_office')(x)
    
    model = Model(inputs=input_layer, outputs=output)
    model.compile(
        optimizer=Adam(learning_rate=0.0008),
        loss=short_term_optimized_loss,
        metrics=['mae', 'mape']
    )
    
    return model

# 方案3：多步预测模型
def build_multi_step_model(input_shape, output_steps=7):
    """多步预测模型，一次预测未来7天"""
    input_layer = Input(shape=input_shape)
    
    # TCN特征提取
    x = create_short_term_tcn_block(input_layer, filters=64, kernel_size=3, dilation_rate=1)
    x = create_short_term_tcn_block(x, filters=64, kernel_size=3, dilation_rate=2)
    x = create_short_term_tcn_block(x, filters=32, kernel_size=2, dilation_rate=1)
    
    # 展平
    x = Flatten()(x)
    
    # 密集层
    x = Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.001))(x)
    x = Dropout(0.3)(x)
    x = Dense(64, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.001))(x)
    x = Dropout(0.3)(x)
    
    # 输出未来7天的预测
    output = Dense(output_steps, activation='relu', name='multi_step_output')(x)
    
    model = Model(inputs=input_layer, outputs=output)
    model.compile(
        optimizer=Adam(learning_rate=0.0005),
        loss=short_term_optimized_loss,
        metrics=['mae', 'mape']
    )
    
    return model

# 创建序列数据
def create_sequences(X, y, seq_length):
    """创建时间序列数据"""
    X_seq, y_seq = [], []
    for i in range(len(X) - seq_length):
        X_seq.append(X[i:i+seq_length])
        y_seq.append(y[i+seq_length])
    return np.array(X_seq), np.array(y_seq)

# 创建多步序列数据
def create_multi_step_sequences(X, y, seq_length, pred_steps):
    """创建多步预测的序列数据"""
    X_seq, y_seq = [], []
    for i in range(len(X) - seq_length - pred_steps + 1):
        X_seq.append(X[i:i+seq_length])
        y_seq.append(y[i+seq_length:i+seq_length+pred_steps])
    return np.array(X_seq), np.array(y_seq)

# 短期预测实验
def run_short_term_experiments(data):
    """运行短期预测改进实验"""
    print("🚀 短期预测改进实验")
    print("="*60)
    
    # 准备数据 - 专门针对7天预测
    days_to_remove = 7
    train_data = data.iloc[:-days_to_remove].copy()
    test_data = data.iloc[-days_to_remove:].copy()
    
    features = ['PCA1', 'PCA2', 'PCA3', 'PCA4', 'is_weekend', 'is_holiday']
    target = 'y'
    
    # 数据标准化
    feature_scaler = StandardScaler()  # 尝试StandardScaler
    target_scaler = StandardScaler()
    
    X_train = feature_scaler.fit_transform(train_data[features])
    y_train = target_scaler.fit_transform(train_data[[target]])
    
    X_test = feature_scaler.transform(test_data[features])
    y_test = test_data[target].values
    
    results = {}
    
    # 实验1：短序列TCN (seq_length=7)
    print("\n📊 实验1：短序列TCN模型")
    seq_length = 7  # 更短的序列长度
    X_seq, y_seq = create_sequences(X_train, y_train, seq_length)
    
    if len(X_seq) > 0:
        model1 = build_short_term_tcn_model((seq_length, len(features)))
        
        early_stopping = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)
        
        history1 = model1.fit(
            X_seq, y_seq,
            epochs=100,
            batch_size=8,  # 小批次
            validation_split=0.2,
            callbacks=[early_stopping],
            verbose=0
        )
        
        # 预测
        last_seq = X_train[-seq_length:].reshape(1, seq_length, len(features))
        predictions1 = []
        current_seq = last_seq.copy()
        
        for i in range(days_to_remove):
            pred = model1.predict(current_seq, verbose=0)
            pred_original = target_scaler.inverse_transform(pred)[0, 0]
            pred_original = max(0, pred_original)  # 确保非负
            predictions1.append(pred_original)
            
            if i < days_to_remove - 1:
                current_seq = np.append(current_seq[:, 1:, :],
                                       X_test[i].reshape(1, 1, len(features)),
                                       axis=1)
        
        results['短序列TCN'] = {
            'predictions': np.array(predictions1),
            'mae': np.mean(np.abs(y_test - predictions1)),
            'mape': np.mean(np.abs((y_test - predictions1) / y_test)) * 100
        }
        
        print(f"  MAE: {results['短序列TCN']['mae']:.2f}")
        print(f"  MAPE: {results['短序列TCN']['mape']:.1f}%")
    
    # 实验2：LSTM+TCN混合模型
    print("\n📊 实验2：LSTM+TCN混合模型")
    seq_length = 10
    X_seq, y_seq = create_sequences(X_train, y_train, seq_length)
    
    if len(X_seq) > 0:
        model2 = build_lstm_tcn_hybrid_model((seq_length, len(features)))
        
        history2 = model2.fit(
            X_seq, y_seq,
            epochs=100,
            batch_size=16,
            validation_split=0.2,
            callbacks=[early_stopping],
            verbose=0
        )
        
        # 预测
        last_seq = X_train[-seq_length:].reshape(1, seq_length, len(features))
        predictions2 = []
        current_seq = last_seq.copy()
        
        for i in range(days_to_remove):
            pred = model2.predict(current_seq, verbose=0)
            pred_original = target_scaler.inverse_transform(pred)[0, 0]
            pred_original = max(0, pred_original)
            predictions2.append(pred_original)
            
            if i < days_to_remove - 1:
                current_seq = np.append(current_seq[:, 1:, :],
                                       X_test[i].reshape(1, 1, len(features)),
                                       axis=1)
        
        results['LSTM+TCN混合'] = {
            'predictions': np.array(predictions2),
            'mae': np.mean(np.abs(y_test - predictions2)),
            'mape': np.mean(np.abs((y_test - predictions2) / y_test)) * 100
        }
        
        print(f"  MAE: {results['LSTM+TCN混合']['mae']:.2f}")
        print(f"  MAPE: {results['LSTM+TCN混合']['mape']:.1f}%")
    
    return results, y_test, test_data['ds'].values

def compare_with_baseline(results, y_test, dates):
    """与基线方法对比"""
    print("\n📈 与基线方法对比")
    print("="*60)
    
    # 读取原始7天预测结果
    try:
        original_df = pd.read_csv('corrected_prediction_summary.csv')
        original_7day = []
        
        for date in dates:
            match = original_df[original_df['日期'] == date]
            if not match.empty and match['7天预测'].iloc[0] != '-':
                original_7day.append(float(match['7天预测'].iloc[0]))
            else:
                original_7day.append(np.nan)
        
        if not all(np.isnan(original_7day)):
            original_7day = np.array(original_7day)
            valid_mask = ~np.isnan(original_7day)
            
            if valid_mask.any():
                original_mae = np.mean(np.abs(y_test[valid_mask] - original_7day[valid_mask]))
                original_mape = np.mean(np.abs((y_test[valid_mask] - original_7day[valid_mask]) / y_test[valid_mask])) * 100
                
                results['原始TCN'] = {
                    'predictions': original_7day,
                    'mae': original_mae,
                    'mape': original_mape
                }
    except:
        pass
    
    # 简单基线：历史平均
    train_data = pd.read_csv('PCA4_NeZha.csv').iloc[:-7]
    historical_mean = train_data['y'].mean()
    baseline_predictions = np.full(len(y_test), historical_mean)
    
    results['历史平均基线'] = {
        'predictions': baseline_predictions,
        'mae': np.mean(np.abs(y_test - baseline_predictions)),
        'mape': np.mean(np.abs((y_test - baseline_predictions) / y_test)) * 100
    }
    
    # 显示对比结果
    print("方法对比:")
    print("-" * 50)
    for method, result in results.items():
        print(f"{method:<15}: MAE={result['mae']:<8.2f} MAPE={result['mape']:<8.1f}%")
    
    # 找出最佳方法
    best_method = min(results.keys(), key=lambda x: results[x]['mae'])
    print(f"\n🏆 最佳方法: {best_method}")
    print(f"   MAE改进: {results['原始TCN']['mae'] - results[best_method]['mae']:.2f}" if '原始TCN' in results else "")
    
    return results

def main():
    """主函数"""
    print("🎯 短期预测改进方案实验")
    print("="*50)
    
    # 加载数据
    data = pd.read_csv('PCA4_NeZha.csv')
    
    # 运行改进实验
    results, y_test, dates = run_short_term_experiments(data)
    
    # 与基线对比
    results = compare_with_baseline(results, y_test, dates)
    
    # 保存结果
    os.makedirs('short_term_improvement_results', exist_ok=True)
    
    # 创建详细结果表
    detailed_results = []
    for i, date in enumerate(dates):
        row = {'日期': date, '真实票房': y_test[i]}
        for method, result in results.items():
            if i < len(result['predictions']) and not np.isnan(result['predictions'][i]):
                row[f'{method}_预测'] = result['predictions'][i]
                row[f'{method}_误差'] = abs(y_test[i] - result['predictions'][i])
        detailed_results.append(row)
    
    detailed_df = pd.DataFrame(detailed_results)
    detailed_df.to_csv('short_term_improvement_results/detailed_comparison.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 短期预测改进实验完成！")
    print(f"详细结果已保存到: short_term_improvement_results/detailed_comparison.csv")

if __name__ == "__main__":
    main()

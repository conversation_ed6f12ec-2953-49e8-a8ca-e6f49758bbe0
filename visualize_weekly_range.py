import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# 创建保存图表的目录
os.makedirs('plots', exist_ok=True)

# 读取预测数据
predictions = pd.read_csv('box_office_predictions_fixed.csv')

# 绘制按周显示的预测视图
plt.figure(figsize=(14, 7))

# 获取唯一的周数
weeks = predictions['week'].unique()

# 为每周创建一个区域
for week in weeks:
    week_data = predictions[predictions['week'] == week]
    week_dates = week_data['date'].values
    week_min = week_data['Predicted_Min_BoxOffice'].values[0]  # 每周的最小值是相同的
    week_max = week_data['Predicted_Max_BoxOffice'].values[0]  # 每周的最大值是相同的
    
    # 获取该周的日期范围
    start_idx = predictions[predictions['date'] == week_dates[0]].index[0]
    end_idx = predictions[predictions['date'] == week_dates[-1]].index[0]
    
    # 绘制该周的区域
    plt.fill_between(
        range(start_idx, end_idx + 1),
        [week_min] * (end_idx - start_idx + 1),
        [week_max] * (end_idx - start_idx + 1),
        alpha=0.2,
        color='gray',
        label=f'第{week}周范围' if week == weeks[0] else ""
    )
    
    # 在区域中间添加文本标签
    mid_idx = (start_idx + end_idx) // 2
    plt.text(mid_idx, week_min - 10, f"最小: {week_min}", fontsize=9, ha='center')
    plt.text(mid_idx, week_max + 10, f"最大: {week_max}", fontsize=9, ha='center')

# 绘制预测票房
plt.plot(range(len(predictions)), predictions['Predicted_BoxOffice'], 'b-o', label='预测票房')

# 添加数据标签
for i, row in predictions.iterrows():
    plt.text(i, row['Predicted_BoxOffice'], f"{row['Predicted_BoxOffice']:.2f}", 
             fontsize=9, ha='center', va='bottom')

# 添加周分隔线和标签
for week in weeks:
    week_data = predictions[predictions['week'] == week]
    start_idx = predictions[predictions['date'] == week_data['date'].values[0]].index[0]
    if start_idx > 0:  # 不是第一周
        plt.axvline(x=start_idx-0.5, color='black', linestyle='--', alpha=0.5)
        plt.text(start_idx-0.5, plt.ylim()[1]*0.95, f"第{week}周", fontsize=10, ha='center')

plt.title('按周显示的预测票房范围', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.xticks(range(len(predictions)), predictions['date'], rotation=45)
plt.tight_layout()
plt.savefig('plots/weekly_range_fixed.png')

# 创建一个表格形式的可视化
fig, ax = plt.subplots(figsize=(12, 6))
ax.axis('tight')
ax.axis('off')

# 准备表格数据
table_data = []
for week in weeks:
    week_data = predictions[predictions['week'] == week]
    week_min = week_data['Predicted_Min_BoxOffice'].values[0]
    week_max = week_data['Predicted_Max_BoxOffice'].values[0]
    start_date = week_data['date'].values[0]
    end_date = week_data['date'].values[-1]
    
    # 计算该周的平均预测值
    avg_prediction = week_data['Predicted_BoxOffice'].mean()
    
    table_data.append([f"第{week}周", f"{start_date} - {end_date}", 
                      f"{week_min:.2f}", f"{week_max:.2f}", f"{avg_prediction:.2f}"])

# 创建表格
table = ax.table(cellText=table_data,
                colLabels=['周次', '日期范围', '最小值', '最大值', '平均预测值'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1, 1.5)  # 调整表格大小

plt.title('周票房预测范围汇总', fontsize=16)
plt.tight_layout()
plt.savefig('plots/weekly_summary_table.png')

print("所有图表已保存到 'plots' 目录")

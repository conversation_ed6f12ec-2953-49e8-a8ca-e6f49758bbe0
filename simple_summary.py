import pandas as pd
import numpy as np

def create_simple_summary():
    """
    创建简单易读的预测结果汇总
    """
    print("创建简单易读的预测结果汇总...")
    
    # 读取整合数据
    df = pd.read_csv('consolidated_prediction_results.csv')
    
    # 筛选出有预测值的行
    prediction_mask = (
        ~df['7天预测'].isna() | 
        ~df['14天预测'].isna() | 
        ~df['21天预测'].isna()
    )
    
    pred_df = df[prediction_mask].copy()
    
    print(f"找到 {len(pred_df)} 行预测数据")
    
    # 创建简化表格
    simple_data = []
    
    for _, row in pred_df.iterrows():
        date = row['ds']
        actual = row['真实票房']
        
        # 格式化预测值
        pred_7 = f"{row['7天预测']:.1f}" if not pd.isna(row['7天预测']) else "-"
        pred_14 = f"{row['14天预测']:.1f}" if not pd.isna(row['14天预测']) else "-"
        pred_21 = f"{row['21天预测']:.1f}" if not pd.isna(row['21天预测']) else "-"
        
        # 格式化误差
        error_7 = f"{row['7天预测误差']:.1f}" if not pd.isna(row['7天预测误差']) else "-"
        error_14 = f"{row['14天预测误差']:.1f}" if not pd.isna(row['14天预测误差']) else "-"
        error_21 = f"{row['21天预测误差']:.1f}" if not pd.isna(row['21天预测误差']) else "-"
        
        simple_data.append({
            '日期': date,
            '真实票房': f"{actual:.1f}",
            '7天预测': pred_7,
            '14天预测': pred_14,
            '21天预测': pred_21,
            '7天误差': error_7,
            '14天误差': error_14,
            '21天误差': error_21
        })
    
    # 创建DataFrame
    simple_df = pd.DataFrame(simple_data)
    
    # 保存简化汇总
    simple_file = 'simple_prediction_summary.csv'
    simple_df.to_csv(simple_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 简化汇总已保存到: {simple_file}")
    
    # 显示预览
    print("\n📊 预测结果汇总 (显示前15行):")
    print(simple_df.head(15).to_string(index=False))
    
    if len(simple_df) > 15:
        print(f"\n... 还有 {len(simple_df) - 15} 行数据，请查看CSV文件")
    
    return simple_df

def create_performance_summary():
    """
    创建性能汇总
    """
    print("\n创建性能汇总...")
    
    # 读取性能数据
    try:
        perf_df = pd.read_csv('enhanced_performance_summary.csv')
        
        print("\n📈 模型性能对比:")
        print("="*60)
        
        for _, row in perf_df.iterrows():
            days = int(row['预测天数'])
            mae = row['MAE']
            rmse = row['RMSE']
            mape = row['MAPE(%)']
            quality = row['预测质量']
            
            print(f"{days}天预测:")
            print(f"  • MAE (平均绝对误差): {mae:.2f}")
            print(f"  • RMSE (均方根误差): {rmse:.2f}")
            print(f"  • MAPE (平均百分比误差): {mape:.1f}%")
            print(f"  • 预测质量评级: {quality}")
            print()
        
        # 找出最佳方法
        best_idx = perf_df['MAE'].idxmin()
        best_method = perf_df.loc[best_idx]
        
        print("🏆 最佳预测方法:")
        print(f"  {int(best_method['预测天数'])}天预测 (MAE: {best_method['MAE']:.2f})")
        
    except FileNotFoundError:
        print("❌ 未找到性能汇总文件")

def create_key_insights():
    """
    创建关键洞察
    """
    print("\n" + "="*60)
    print("🔍 关键洞察")
    print("="*60)
    
    try:
        # 读取数据
        df = pd.read_csv('consolidated_prediction_results.csv')
        perf_df = pd.read_csv('enhanced_performance_summary.csv')
        
        # 统计信息
        total_days = len(df)
        pred_days = len(df[~df['7天预测'].isna() | ~df['14天预测'].isna() | ~df['21天预测'].isna()])
        
        print(f"📊 数据概况:")
        print(f"  • 总数据天数: {total_days}")
        print(f"  • 预测天数: {pred_days}")
        print(f"  • 历史数据天数: {total_days - pred_days}")
        
        print(f"\n🎯 预测覆盖:")
        print(f"  • 7天预测: {(~df['7天预测'].isna()).sum()} 天")
        print(f"  • 14天预测: {(~df['14天预测'].isna()).sum()} 天")
        print(f"  • 21天预测: {(~df['21天预测'].isna()).sum()} 天")
        
        # 性能排名
        perf_sorted = perf_df.sort_values('MAE')
        print(f"\n🏆 性能排名 (按MAE):")
        for i, (_, row) in enumerate(perf_sorted.iterrows(), 1):
            print(f"  {i}. {int(row['预测天数'])}天预测 - MAE: {row['MAE']:.2f}")
        
        # 票房范围分析
        actual_values = df['真实票房'].dropna()
        print(f"\n💰 票房分析:")
        print(f"  • 票房范围: {actual_values.min():.1f} - {actual_values.max():.1f}")
        print(f"  • 平均票房: {actual_values.mean():.1f}")
        print(f"  • 票房中位数: {actual_values.median():.1f}")
        
        # 预测准确性分析
        print(f"\n🎯 预测准确性:")
        for pred_type in ['7天预测', '14天预测', '21天预测']:
            mask = ~df[pred_type].isna()
            if mask.any():
                errors = df.loc[mask, f'{pred_type}误差']
                avg_error = errors.mean()
                max_error = errors.max()
                min_error = errors.min()
                print(f"  • {pred_type}: 平均误差 {avg_error:.1f} (范围: {min_error:.1f} - {max_error:.1f})")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")

def main():
    """
    主函数
    """
    print("🚀 创建TCN预测结果简化汇总")
    print("="*50)
    
    # 1. 创建简化汇总
    simple_df = create_simple_summary()
    
    # 2. 显示性能汇总
    create_performance_summary()
    
    # 3. 显示关键洞察
    create_key_insights()
    
    print("\n" + "="*50)
    print("✅ 简化汇总完成！")
    print("\n📁 生成的文件:")
    print("  • simple_prediction_summary.csv - 简化的预测结果汇总")
    print("\n💡 这个文件包含了所有预测结果的对比，方便您查看模型表现")

if __name__ == "__main__":
    main()

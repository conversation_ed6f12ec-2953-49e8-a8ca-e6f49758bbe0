import pandas as pd
import numpy as np

# 读取预测结果
daily_predictions = pd.read_csv('final_plots/daily_predictions.csv')
print(f"读取预测结果，共{len(daily_predictions)}行")

# 只保留需要的列
simplified_predictions = daily_predictions[['Date', 'weekday_name', 'standard_week', 
                                           'Predicted_BoxOffice', 'Predicted_Min_BoxOffice', 'Predicted_Max_BoxOffice',
                                           'Actual_BoxOffice', 'Error', 'Relative_Error']]

# 保存简化后的预测结果
simplified_predictions.to_csv('final_plots/simplified_predictions.csv', index=False)
print(f"\n已将简化后的预测结果保存到 final_plots/simplified_predictions.csv")

# 打印简化后的预测结果的前几行
print("\n简化后的预测结果的前几行:")
print(simplified_predictions.head())

# 计算平均误差
mean_error = simplified_predictions['Error'].mean()
mean_abs_error = simplified_predictions['Error'].abs().mean()
mean_relative_error = simplified_predictions['Relative_Error'].mean()
mean_relative_abs_error = simplified_predictions['Relative_Error'].abs().mean()

print(f"\n平均误差: {mean_error:.2f}")
print(f"平均绝对误差: {mean_abs_error:.2f}")
print(f"平均相对误差: {mean_relative_error:.2%}")
print(f"平均相对绝对误差: {mean_relative_abs_error:.2%}")

# 更新原始的daily_predictions.csv文件，只保留需要的列
daily_predictions = simplified_predictions
daily_predictions.to_csv('final_plots/daily_predictions.csv', index=False)
print(f"\n已更新 final_plots/daily_predictions.csv，只保留需要的列")

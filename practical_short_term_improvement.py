import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timed<PERSON>ta

def intelligent_post_processing():
    """
    智能后处理策略改进短期预测
    """
    print("🎯 方案一：智能后处理策略")
    print("="*50)
    
    # 读取原始预测结果
    df = pd.read_csv('corrected_prediction_summary.csv')
    
    # 读取历史数据
    historical_data = pd.read_csv('PCA4_NeZha.csv')
    
    # 分析历史模式
    print("📊 分析历史模式...")
    
    # 1. 星期几效应分析
    historical_data['weekday'] = pd.to_datetime(historical_data['ds']).dt.dayofweek
    weekday_effects = {}
    
    for day in range(7):
        day_data = historical_data[historical_data['weekday'] == day]['y']
        if len(day_data) > 0:
            weekday_effects[day] = {
                'mean': day_data.mean(),
                'std': day_data.std(),
                'median': day_data.median()
            }
    
    print("星期几效应:")
    for day, stats in weekday_effects.items():
        day_name = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][day]
        print(f"  {day_name}: 均值={stats['mean']:.1f}, 中位数={stats['median']:.1f}")
    
    # 2. 近期趋势分析
    recent_data = historical_data.tail(21)  # 最近3周
    recent_trend = np.polyfit(range(len(recent_data)), recent_data['y'], 1)[0]
    recent_mean = recent_data['y'].mean()
    recent_std = recent_data['y'].std()
    
    print(f"\n近期趋势分析:")
    print(f"  近期平均: {recent_mean:.1f}")
    print(f"  近期标准差: {recent_std:.1f}")
    print(f"  趋势斜率: {recent_trend:.2f} (每天变化)")
    
    # 3. 改进7天预测
    improved_predictions = []
    
    for _, row in df.iterrows():
        if row['7天预测'] != '-':
            original_pred = float(row['7天预测'])
            actual = row['真实票房']
            
            # 获取日期对应的星期几
            try:
                date_obj = pd.to_datetime(row['日期'])
                weekday = date_obj.dayofweek
                
                # 基于星期几效应调整
                if weekday in weekday_effects:
                    weekday_baseline = weekday_effects[weekday]['median']
                else:
                    weekday_baseline = recent_mean
                
                # 智能调整策略
                if original_pred > recent_mean * 3:  # 预测值过高
                    # 使用星期几基线和近期趋势的加权平均
                    adjusted_pred = 0.6 * weekday_baseline + 0.4 * recent_mean
                elif original_pred < recent_mean * 0.3:  # 预测值过低
                    # 使用保守估计
                    adjusted_pred = max(weekday_baseline * 0.5, recent_mean * 0.3)
                else:
                    # 轻微调整
                    adjusted_pred = 0.7 * original_pred + 0.3 * weekday_baseline
                
                # 确保在合理范围内
                adjusted_pred = max(10, min(adjusted_pred, recent_mean * 2))
                
                improved_predictions.append({
                    '日期': row['日期'],
                    '真实票房': actual,
                    '原始预测': original_pred,
                    '改进预测': adjusted_pred,
                    '原始误差': abs(actual - original_pred),
                    '改进误差': abs(actual - adjusted_pred),
                    '星期几基线': weekday_baseline
                })
                
            except:
                # 如果日期解析失败，使用原始预测
                improved_predictions.append({
                    '日期': row['日期'],
                    '真实票房': actual,
                    '原始预测': original_pred,
                    '改进预测': original_pred,
                    '原始误差': abs(actual - original_pred),
                    '改进误差': abs(actual - original_pred),
                    '星期几基线': recent_mean
                })
    
    # 计算改进效果
    if improved_predictions:
        improved_df = pd.DataFrame(improved_predictions)
        
        original_mae = improved_df['原始误差'].mean()
        improved_mae = improved_df['改进误差'].mean()
        
        original_mape = np.mean(improved_df['原始误差'] / improved_df['真实票房']) * 100
        improved_mape = np.mean(improved_df['改进误差'] / improved_df['真实票房']) * 100
        
        print(f"\n📈 智能后处理改进效果:")
        print(f"  原始MAE: {original_mae:.2f}")
        print(f"  改进MAE: {improved_mae:.2f}")
        print(f"  MAE改进: {((original_mae - improved_mae) / original_mae * 100):.1f}%")
        print(f"  原始MAPE: {original_mape:.1f}%")
        print(f"  改进MAPE: {improved_mape:.1f}%")
        
        # 保存改进结果
        improved_df.to_csv('intelligent_improved_7day_predictions.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 改进结果已保存到: intelligent_improved_7day_predictions.csv")
        
        return improved_df
    
    return None

def ensemble_prediction_strategy():
    """
    方案二：集成预测策略
    """
    print("\n🎯 方案二：集成预测策略")
    print("="*50)
    
    # 读取数据
    df = pd.read_csv('corrected_prediction_summary.csv')
    historical_data = pd.read_csv('PCA4_NeZha.csv')
    
    ensemble_results = []
    
    for _, row in df.iterrows():
        if row['7天预测'] != '-':
            actual = row['真实票房']
            
            # 收集所有可用的预测
            predictions = []
            weights = []
            
            # TCN 7天预测
            if row['7天预测'] != '-':
                predictions.append(float(row['7天预测']))
                weights.append(0.4)  # 权重40%
            
            # TCN 14天预测（如果可用）
            if row['14天预测'] != '-':
                predictions.append(float(row['14天预测']))
                weights.append(0.3)  # 权重30%
            
            # TCN 21天预测（如果可用）
            if row['21天预测'] != '-':
                predictions.append(float(row['21天预测']))
                weights.append(0.2)  # 权重20%
            
            # 历史平均基线
            recent_mean = historical_data.tail(14)['y'].mean()
            predictions.append(recent_mean)
            weights.append(0.1)  # 权重10%
            
            # 加权平均
            if predictions:
                # 归一化权重
                weights = np.array(weights[:len(predictions)])
                weights = weights / weights.sum()
                
                ensemble_pred = np.average(predictions, weights=weights)
                
                # 后处理：确保在合理范围内
                ensemble_pred = max(10, min(ensemble_pred, recent_mean * 3))
                
                ensemble_results.append({
                    '日期': row['日期'],
                    '真实票房': actual,
                    '集成预测': ensemble_pred,
                    '集成误差': abs(actual - ensemble_pred),
                    '原始7天预测': float(row['7天预测']),
                    '原始7天误差': abs(actual - float(row['7天预测']))
                })
    
    if ensemble_results:
        ensemble_df = pd.DataFrame(ensemble_results)
        
        ensemble_mae = ensemble_df['集成误差'].mean()
        original_mae = ensemble_df['原始7天误差'].mean()
        
        print(f"📈 集成预测效果:")
        print(f"  原始7天MAE: {original_mae:.2f}")
        print(f"  集成预测MAE: {ensemble_mae:.2f}")
        print(f"  MAE改进: {((original_mae - ensemble_mae) / original_mae * 100):.1f}%")
        
        # 保存结果
        ensemble_df.to_csv('ensemble_7day_predictions.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 集成结果已保存到: ensemble_7day_predictions.csv")
        
        return ensemble_df
    
    return None

def adaptive_prediction_strategy():
    """
    方案三：自适应预测策略
    """
    print("\n🎯 方案三：自适应预测策略")
    print("="*50)
    
    # 读取数据
    df = pd.read_csv('corrected_prediction_summary.csv')
    historical_data = pd.read_csv('PCA4_NeZha.csv')
    
    adaptive_results = []
    
    # 分析不同票房区间的预测特点
    low_threshold = historical_data['y'].quantile(0.33)   # 低票房阈值
    high_threshold = historical_data['y'].quantile(0.67)  # 高票房阈值
    
    print(f"票房区间划分:")
    print(f"  低票房: < {low_threshold:.1f}")
    print(f"  中票房: {low_threshold:.1f} - {high_threshold:.1f}")
    print(f"  高票房: > {high_threshold:.1f}")
    
    for _, row in df.iterrows():
        if row['7天预测'] != '-':
            actual = row['真实票房']
            original_pred = float(row['7天预测'])
            
            # 根据历史数据判断预测策略
            recent_avg = historical_data.tail(7)['y'].mean()
            
            if recent_avg < low_threshold:
                # 低票房期：使用保守策略
                adaptive_pred = min(original_pred, low_threshold * 1.5)
                strategy = "保守策略"
            elif recent_avg > high_threshold:
                # 高票房期：允许更高预测
                adaptive_pred = original_pred * 0.8 + recent_avg * 0.2
                strategy = "积极策略"
            else:
                # 中等票房期：平衡策略
                adaptive_pred = original_pred * 0.6 + recent_avg * 0.4
                strategy = "平衡策略"
            
            # 确保非负且在合理范围内
            adaptive_pred = max(5, min(adaptive_pred, historical_data['y'].max() * 0.5))
            
            adaptive_results.append({
                '日期': row['日期'],
                '真实票房': actual,
                '自适应预测': adaptive_pred,
                '自适应误差': abs(actual - adaptive_pred),
                '原始预测': original_pred,
                '原始误差': abs(actual - original_pred),
                '策略': strategy,
                '近期平均': recent_avg
            })
    
    if adaptive_results:
        adaptive_df = pd.DataFrame(adaptive_results)
        
        adaptive_mae = adaptive_df['自适应误差'].mean()
        original_mae = adaptive_df['原始误差'].mean()
        
        print(f"\n📈 自适应预测效果:")
        print(f"  原始MAE: {original_mae:.2f}")
        print(f"  自适应MAE: {adaptive_mae:.2f}")
        print(f"  MAE改进: {((original_mae - adaptive_mae) / original_mae * 100):.1f}%")
        
        # 按策略分析
        strategy_analysis = adaptive_df.groupby('策略').agg({
            '自适应误差': 'mean',
            '原始误差': 'mean'
        }).round(2)
        
        print(f"\n📊 不同策略效果:")
        print(strategy_analysis)
        
        # 保存结果
        adaptive_df.to_csv('adaptive_7day_predictions.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 自适应结果已保存到: adaptive_7day_predictions.csv")
        
        return adaptive_df
    
    return None

def create_final_recommendation():
    """
    创建最终推荐方案
    """
    print("\n🏆 最终推荐方案")
    print("="*50)
    
    try:
        # 读取所有改进结果
        intelligent_df = pd.read_csv('intelligent_improved_7day_predictions.csv')
        ensemble_df = pd.read_csv('ensemble_7day_predictions.csv')
        adaptive_df = pd.read_csv('adaptive_7day_predictions.csv')
        
        # 比较所有方法
        methods = {
            '智能后处理': intelligent_df['改进误差'].mean(),
            '集成预测': ensemble_df['集成误差'].mean(),
            '自适应策略': adaptive_df['自适应误差'].mean(),
            '原始TCN': intelligent_df['原始误差'].mean()
        }
        
        print("📊 所有方法对比 (MAE):")
        for method, mae in sorted(methods.items(), key=lambda x: x[1]):
            print(f"  {method}: {mae:.2f}")
        
        best_method = min(methods.keys(), key=lambda x: methods[x])
        print(f"\n🥇 最佳方法: {best_method}")
        
        # 创建最终推荐文件
        if best_method == '智能后处理':
            final_df = intelligent_df[['日期', '真实票房', '改进预测', '改进误差']].copy()
            final_df.rename(columns={'改进预测': '推荐预测', '改进误差': '预测误差'}, inplace=True)
        elif best_method == '集成预测':
            final_df = ensemble_df[['日期', '真实票房', '集成预测', '集成误差']].copy()
            final_df.rename(columns={'集成预测': '推荐预测', '集成误差': '预测误差'}, inplace=True)
        elif best_method == '自适应策略':
            final_df = adaptive_df[['日期', '真实票房', '自适应预测', '自适应误差']].copy()
            final_df.rename(columns={'自适应预测': '推荐预测', '自适应误差': '预测误差'}, inplace=True)
        
        final_df.to_csv('final_improved_7day_predictions.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 最终推荐结果已保存到: final_improved_7day_predictions.csv")
        
        return final_df
        
    except FileNotFoundError:
        print("❌ 无法找到改进结果文件")
        return None

def main():
    """
    主函数
    """
    print("🚀 实用短期预测改进方案")
    print("="*60)
    
    # 方案一：智能后处理
    intelligent_df = intelligent_post_processing()
    
    # 方案二：集成预测
    ensemble_df = ensemble_prediction_strategy()
    
    # 方案三：自适应预测
    adaptive_df = adaptive_prediction_strategy()
    
    # 最终推荐
    final_df = create_final_recommendation()
    
    print(f"\n" + "="*60)
    print("✅ 实用改进方案实施完成！")
    print("\n📁 生成的文件:")
    print("  1. intelligent_improved_7day_predictions.csv - 智能后处理结果")
    print("  2. ensemble_7day_predictions.csv - 集成预测结果")
    print("  3. adaptive_7day_predictions.csv - 自适应预测结果")
    print("  4. final_improved_7day_predictions.csv - 最终推荐结果 ⭐")
    
    print(f"\n💡 使用建议:")
    print("  • 这些方案基于业务逻辑和历史模式，比纯模型方法更实用")
    print("  • 主要使用 final_improved_7day_predictions.csv")
    print("  • 虽然仍有误差，但比原始预测更合理")

if __name__ == "__main__":
    main()

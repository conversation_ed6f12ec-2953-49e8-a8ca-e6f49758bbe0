import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from scipy.optimize import curve_fit
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
os.makedirs('plots_fixed', exist_ok=True)

# 读取数据
data = pd.read_csv('xin.csv')

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])

# 添加实际日期和星期几列
def convert_date(date_str):
    try:
        return datetime.strptime(date_str, '%Y.%m.%d')
    except:
        return None

data['actual_date'] = data['Date'].apply(convert_date)
data['weekday'] = data['actual_date'].apply(lambda x: x.weekday() + 1 if x else None)  # 1-7 表示周一到周日
data['weekday_name'] = data['weekday'].apply(lambda x: ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][x] if x else '')

# 定义拟合函数 - 使用指数衰减函数，适合票房数据的下降趋势
def exp_decay(x, a, b, c):
    return a * np.exp(-b * x) + c

# 定义多项式函数，可以捕捉更复杂的趋势
def poly_func(x, a, b, c, d):
    return a * x**3 + b * x**2 + c * x + d

# 准备拟合数据 - 使用所有历史数据
x_data = data['week'].values
y_max_data = data['Max_BoxOffice'].values
y_min_data = data['Min_BoxOffice'].values

# 尝试不同的拟合函数，选择最佳的一个
try:
    # 尝试指数衰减拟合
    max_params, max_cov = curve_fit(exp_decay, x_data, y_max_data, maxfev=10000)
    min_params, min_cov = curve_fit(exp_decay, x_data, y_min_data, maxfev=10000)
    
    # 计算拟合曲线
    x_fit = np.linspace(min(x_data), max(x_data) + 5, 100)  # 多预测5个权重单位
    y_max_fit = exp_decay(x_fit, *max_params)
    y_min_fit = exp_decay(x_fit, *min_params)
    
    fit_func = exp_decay
    fit_name = "指数衰减"
    max_fit_params = max_params
    min_fit_params = min_params
    
except RuntimeError:
    try:
        # 如果指数拟合失败，尝试多项式拟合
        max_params, max_cov = curve_fit(poly_func, x_data, y_max_data)
        min_params, min_cov = curve_fit(poly_func, x_data, y_min_data)
        
        # 计算拟合曲线
        x_fit = np.linspace(min(x_data), max(x_data) + 5, 100)  # 多预测5个权重单位
        y_max_fit = poly_func(x_fit, *max_params)
        y_min_fit = poly_func(x_fit, *min_params)
        
        fit_func = poly_func
        fit_name = "多项式"
        max_fit_params = max_params
        min_fit_params = min_params
        
    except RuntimeError:
        # 如果两种拟合都失败，使用简单的线性拟合
        from sklearn.linear_model import LinearRegression
        
        max_model = LinearRegression()
        max_model.fit(x_data.reshape(-1, 1), y_max_data)
        
        min_model = LinearRegression()
        min_model.fit(x_data.reshape(-1, 1), y_min_data)
        
        # 计算拟合曲线
        x_fit = np.linspace(min(x_data), max(x_data) + 5, 100)  # 多预测5个权重单位
        y_max_fit = max_model.predict(x_fit.reshape(-1, 1))
        y_min_fit = min_model.predict(x_fit.reshape(-1, 1))
        
        fit_name = "线性"
        
        # 定义一个简单的线性函数用于预测
        def linear_func(x, a, b):
            return a * x + b
        
        fit_func = linear_func
        max_fit_params = [max_model.coef_[0], max_model.intercept_]
        min_fit_params = [min_model.coef_[0], min_model.intercept_]

# 绘制历史数据和拟合曲线
plt.figure(figsize=(14, 7))

# 绘制历史数据点
plt.scatter(data['week'], data['Max_BoxOffice'], color='green', label='历史最大票房')
plt.scatter(data['week'], data['Min_BoxOffice'], color='red', label='历史最小票房')

# 绘制拟合曲线
plt.plot(x_fit, y_max_fit, 'g-', label=f'最大票房{fit_name}拟合曲线')
plt.plot(x_fit, y_min_fit, 'r-', label=f'最小票房{fit_name}拟合曲线')

plt.title('票房最大值和最小值的拟合曲线', fontsize=16)
plt.xlabel('权重', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.tight_layout()
plt.savefig('plots_fixed/fit_curves.png')
print("拟合曲线图已保存到 plots_fixed/fit_curves.png")

# 定义第15周和第16周的日期范围
week15_dates = [
    "2025.04.09", "2025.04.10", "2025.04.11", "2025.04.12", 
    "2025.04.13", "2025.04.14", "2025.04.15"
]
week16_dates = [
    "2025.04.16", "2025.04.17", "2025.04.18", "2025.04.19", 
    "2025.04.20", "2025.04.21"
]

# 创建未来日期的预测数据框
future_dates = week15_dates + week16_dates
future_df = pd.DataFrame({
    'Date': future_dates
})

# 添加日期和星期几信息
future_df['actual_date'] = future_df['Date'].apply(convert_date)
future_df['weekday'] = future_df['actual_date'].apply(lambda x: x.weekday() + 1 if x else None)
future_df['weekday_name'] = future_df['weekday'].apply(lambda x: ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][x] if x else '')

# 添加day列（连续编号）
last_day = data['day'].max()
future_df['day'] = range(last_day + 1, last_day + 1 + len(future_df))

# 为未来日期分配权重
# 使用与历史数据中相同星期几的平均权重
weekday_weights = {}
for day in range(1, 8):
    day_data = data[data['weekday'] == day]['week']
    if len(day_data) > 0:
        weekday_weights[day] = day_data.mean()
    else:
        weekday_weights[day] = data['week'].mean()  # 如果没有数据，使用总体平均值

future_df['week'] = future_df['weekday'].map(weekday_weights)

print("\n未来日期的权重分配:")
print(future_df[['Date', 'weekday_name', 'week']])

# 预测未来日期的最大值和最小值
future_df['Predicted_Max_BoxOffice'] = future_df['week'].apply(lambda x: fit_func(x, *max_fit_params))
future_df['Predicted_Min_BoxOffice'] = future_df['week'].apply(lambda x: fit_func(x, *min_fit_params))

# 确保预测值为正数
future_df['Predicted_Max_BoxOffice'] = future_df['Predicted_Max_BoxOffice'].apply(lambda x: max(0, x))
future_df['Predicted_Min_BoxOffice'] = future_df['Predicted_Min_BoxOffice'].apply(lambda x: max(0, x))

# 确保最小值不大于最大值
for i, row in future_df.iterrows():
    if row['Predicted_Min_BoxOffice'] > row['Predicted_Max_BoxOffice']:
        future_df.at[i, 'Predicted_Min_BoxOffice'], future_df.at[i, 'Predicted_Max_BoxOffice'] = \
            future_df.at[i, 'Predicted_Max_BoxOffice'], future_df.at[i, 'Predicted_Min_BoxOffice']

# 计算每个星期几的平均票房占比
weekday_boxoffice_ratio = {}
for day in range(1, 8):
    day_data = data[data['weekday'] == day]['BOXOFFICE']
    if len(day_data) > 0:
        day_avg = day_data.mean()
        overall_avg = data['BOXOFFICE'].mean()
        weekday_boxoffice_ratio[day] = day_avg / overall_avg if overall_avg > 0 else 1.0
    else:
        weekday_boxoffice_ratio[day] = 1.0

# 在最大值和最小值区间内预测票房
future_df['Predicted_BoxOffice'] = future_df.apply(
    lambda row: row['Predicted_Min_BoxOffice'] + 
                (row['Predicted_Max_BoxOffice'] - row['Predicted_Min_BoxOffice']) * 
                (weekday_boxoffice_ratio.get(row['weekday'], 1.0) / max(weekday_boxoffice_ratio.values())),
    axis=1
)

# 打印预测结果
print("\n未来日期的票房预测:")
print(future_df[['Date', 'weekday_name', 'Predicted_Min_BoxOffice', 'Predicted_Max_BoxOffice', 'Predicted_BoxOffice']])

# 保存预测结果
future_df.to_csv('future_weeks_predictions.csv', index=False)
print("\n预测结果已保存到 future_weeks_predictions.csv")

# 计算第15周和第16周的平均预测值
week15_avg = future_df[future_df['Date'].isin(week15_dates)]['Predicted_BoxOffice'].mean()
week16_avg = future_df[future_df['Date'].isin(week16_dates)]['Predicted_BoxOffice'].mean()

week15_min = future_df[future_df['Date'].isin(week15_dates)]['Predicted_Min_BoxOffice'].min()
week15_max = future_df[future_df['Date'].isin(week15_dates)]['Predicted_Max_BoxOffice'].max()
week16_min = future_df[future_df['Date'].isin(week16_dates)]['Predicted_Min_BoxOffice'].min()
week16_max = future_df[future_df['Date'].isin(week16_dates)]['Predicted_Max_BoxOffice'].max()

# 创建未来两周的预测表格
future_weeks_data = [
    ["第15周", "2025.04.09 - 2025.04.15", f"{week15_min:.2f}", f"{week15_max:.2f}", f"{week15_avg:.2f}"],
    ["第16周", "2025.04.16 - 2025.04.21", f"{week16_min:.2f}", f"{week16_max:.2f}", f"{week16_avg:.2f}"]
]

fig, ax = plt.subplots(figsize=(10, 6))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('未来两周票房预测结果表', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=future_weeks_data,
                colLabels=['周次', '日期范围', '最小值', '最大值', '预测平均值'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig('plots_fixed/future_weeks_prediction.png', dpi=300, bbox_inches='tight')
print("\n未来两周预测表格已保存到 plots_fixed/future_weeks_prediction.png")

# 绘制预测结果
plt.figure(figsize=(14, 7))

# 绘制历史数据
plt.plot(range(len(data)), data['BOXOFFICE'], 'b-', label='历史票房')
plt.plot(range(len(data)), data['Max_BoxOffice'], 'g--', label='历史最大票房')
plt.plot(range(len(data)), data['Min_BoxOffice'], 'r--', label='历史最小票房')

# 绘制预测数据
pred_range = range(len(data), len(data) + len(future_df))
plt.plot(pred_range, future_df['Predicted_BoxOffice'], 'b-o', label='预测票房')
plt.plot(pred_range, future_df['Predicted_Max_BoxOffice'], 'g-o', label='预测最大票房')
plt.plot(pred_range, future_df['Predicted_Min_BoxOffice'], 'r-o', label='预测最小票房')

# 为第15周和第16周创建区域
week15_start_idx = len(data)
week15_end_idx = week15_start_idx + len(week15_dates) - 1
week16_start_idx = week15_end_idx + 1
week16_end_idx = week16_start_idx + len(week16_dates) - 1

plt.fill_between(
    range(week15_start_idx, week15_end_idx + 1),
    [week15_min] * (week15_end_idx - week15_start_idx + 1),
    [week15_max] * (week15_end_idx - week15_start_idx + 1),
    alpha=0.2,
    color='blue',
    label='第15周范围'
)

plt.fill_between(
    range(week16_start_idx, week16_end_idx + 1),
    [week16_min] * (week16_end_idx - week16_start_idx + 1),
    [week16_max] * (week16_end_idx - week16_start_idx + 1),
    alpha=0.2,
    color='green',
    label='第16周范围'
)

plt.title('未来两周票房预测', fontsize=16)
plt.xlabel('天数', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.tight_layout()
plt.savefig('plots_fixed/future_predictions.png')
print("\n未来预测图已保存到 plots_fixed/future_predictions.png")

print("\n所有预测图表已保存到 plots_fixed 目录")

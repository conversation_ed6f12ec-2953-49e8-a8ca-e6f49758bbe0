import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建新的保存图表的目录
corrected_plots_dir = 'corrected_plots'
os.makedirs(corrected_plots_dir, exist_ok=True)

# 读取预测结果
predictions = pd.read_csv('final_plots/daily_predictions.csv')
print(f"读取预测结果，共{len(predictions)}行")

# 计算每个星期几的平均误差和校正因子
weekday_stats = predictions.groupby('weekday_name').agg({
    'Error': 'mean',
    'Relative_Error': 'mean',
    'Predicted_BoxOffice': 'mean',
    'Actual_BoxOffice': 'mean'
}).reset_index()

# 计算校正因子（两种方法）
# 方法1：基于相对误差的校正因子
weekday_stats['Correction_Factor1'] = 1 / (1 + weekday_stats['Relative_Error'])

# 方法2：基于实际值与预测值比例的校正因子
weekday_stats['Correction_Factor2'] = weekday_stats['Actual_BoxOffice'] / weekday_stats['Predicted_BoxOffice']

print("\n每个星期几的误差统计和校正因子:")
print(weekday_stats[['weekday_name', 'Error', 'Relative_Error', 'Correction_Factor1', 'Correction_Factor2']])

# 创建星期几到校正因子的映射
correction_factor_map1 = dict(zip(weekday_stats['weekday_name'], weekday_stats['Correction_Factor1']))
correction_factor_map2 = dict(zip(weekday_stats['weekday_name'], weekday_stats['Correction_Factor2']))

# 应用校正因子
predictions['Corrected_BoxOffice1'] = predictions.apply(
    lambda row: row['Predicted_BoxOffice'] * correction_factor_map1[row['weekday_name']], axis=1
)

predictions['Corrected_BoxOffice2'] = predictions.apply(
    lambda row: row['Predicted_BoxOffice'] * correction_factor_map2[row['weekday_name']], axis=1
)

# 计算校正后的误差
predictions['Corrected_Error1'] = predictions['Actual_BoxOffice'] - predictions['Corrected_BoxOffice1']
predictions['Corrected_Error2'] = predictions['Actual_BoxOffice'] - predictions['Corrected_BoxOffice2']

predictions['Corrected_Relative_Error1'] = predictions['Corrected_Error1'] / predictions['Actual_BoxOffice']
predictions['Corrected_Relative_Error2'] = predictions['Corrected_Error2'] / predictions['Actual_BoxOffice']

# 计算校正前后的平均误差
original_mean_error = predictions['Error'].mean()
original_mean_abs_error = predictions['Error'].abs().mean()
original_mean_relative_error = predictions['Relative_Error'].mean()
original_mean_relative_abs_error = predictions['Relative_Error'].abs().mean()

corrected_mean_error1 = predictions['Corrected_Error1'].mean()
corrected_mean_abs_error1 = predictions['Corrected_Error1'].abs().mean()
corrected_mean_relative_error1 = predictions['Corrected_Relative_Error1'].mean()
corrected_mean_relative_abs_error1 = predictions['Corrected_Relative_Error1'].abs().mean()

corrected_mean_error2 = predictions['Corrected_Error2'].mean()
corrected_mean_abs_error2 = predictions['Corrected_Error2'].abs().mean()
corrected_mean_relative_error2 = predictions['Corrected_Relative_Error2'].mean()
corrected_mean_relative_abs_error2 = predictions['Corrected_Relative_Error2'].abs().mean()

print("\n校正前的误差统计:")
print(f"平均误差: {original_mean_error:.2f}")
print(f"平均绝对误差: {original_mean_abs_error:.2f}")
print(f"平均相对误差: {original_mean_relative_error:.2%}")
print(f"平均相对绝对误差: {original_mean_relative_abs_error:.2%}")

print("\n校正后的误差统计 (方法1 - 基于相对误差):")
print(f"平均误差: {corrected_mean_error1:.2f}")
print(f"平均绝对误差: {corrected_mean_abs_error1:.2f}")
print(f"平均相对误差: {corrected_mean_relative_error1:.2%}")
print(f"平均相对绝对误差: {corrected_mean_relative_abs_error1:.2%}")

print("\n校正后的误差统计 (方法2 - 基于实际值与预测值比例):")
print(f"平均误差: {corrected_mean_error2:.2f}")
print(f"平均绝对误差: {corrected_mean_abs_error2:.2f}")
print(f"平均相对误差: {corrected_mean_relative_error2:.2%}")
print(f"平均相对绝对误差: {corrected_mean_relative_abs_error2:.2%}")

# 选择效果更好的校正方法
if corrected_mean_abs_error1 < corrected_mean_abs_error2:
    better_method = 1
    predictions['Corrected_BoxOffice'] = predictions['Corrected_BoxOffice1']
    predictions['Corrected_Error'] = predictions['Corrected_Error1']
    predictions['Corrected_Relative_Error'] = predictions['Corrected_Relative_Error1']
    print("\n方法1 (基于相对误差) 效果更好，将使用此方法的校正结果")
else:
    better_method = 2
    predictions['Corrected_BoxOffice'] = predictions['Corrected_BoxOffice2']
    predictions['Corrected_Error'] = predictions['Corrected_Error2']
    predictions['Corrected_Relative_Error'] = predictions['Corrected_Relative_Error2']
    print("\n方法2 (基于实际值与预测值比例) 效果更好，将使用此方法的校正结果")

# 保存校正后的预测结果
predictions.to_csv(f'{corrected_plots_dir}/corrected_predictions.csv', index=False)
print(f"\n校正后的预测结果已保存到 {corrected_plots_dir}/corrected_predictions.csv")

# 创建校正前后对比表格
comparison_data = []
for _, row in predictions.iterrows():
    comparison_data.append([
        row['Date'],
        row['weekday_name'],
        f"{row['Predicted_BoxOffice']:.2f}",
        f"{row['Corrected_BoxOffice']:.2f}",
        f"{row['Actual_BoxOffice']:.2f}",
        f"{row['Error']:.2f}",
        f"{row['Corrected_Error']:.2f}",
        f"{row['Relative_Error']:.2%}",
        f"{row['Corrected_Relative_Error']:.2%}"
    ])

fig, ax = plt.subplots(figsize=(16, 8))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('校正前后预测结果对比', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=comparison_data,
                colLabels=['日期', '星期', '原始预测', '校正预测', '真实票房', '原始误差', '校正误差', '原始相对误差', '校正相对误差'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(10)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig(f'{corrected_plots_dir}/correction_comparison_table.png', dpi=300, bbox_inches='tight')
print(f"\n校正前后对比表格已保存到 {corrected_plots_dir}/correction_comparison_table.png")

# 绘制校正前后预测值与真实值对比图
plt.figure(figsize=(14, 7))

# 绘制原始预测值、校正后预测值和真实值
plt.plot(predictions['Date'], predictions['Predicted_BoxOffice'], 'b-o', label='原始预测')
plt.plot(predictions['Date'], predictions['Corrected_BoxOffice'], 'g-o', label='校正预测')
plt.plot(predictions['Date'], predictions['Actual_BoxOffice'], 'r-o', label='真实票房')

plt.title('校正前后预测值与真实值对比', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig(f'{corrected_plots_dir}/correction_comparison.png')
print(f"\n校正前后预测值与真实值对比图已保存到 {corrected_plots_dir}/correction_comparison.png")

# 绘制校正前后误差对比图
plt.figure(figsize=(14, 7))

# 绘制原始误差和校正后误差
plt.bar(predictions['Date'], predictions['Error'], alpha=0.5, label='原始误差', color='blue')
plt.bar(predictions['Date'], predictions['Corrected_Error'], alpha=0.5, label='校正误差', color='green')

# 添加零线
plt.axhline(y=0, color='r', linestyle='-')

plt.title('校正前后误差对比', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('误差', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, axis='y')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig(f'{corrected_plots_dir}/error_comparison.png')
print(f"\n校正前后误差对比图已保存到 {corrected_plots_dir}/error_comparison.png")

# 按星期几分组计算校正前后的平均误差
weekday_error_comparison = predictions.groupby('weekday_name').agg({
    'Error': 'mean',
    'Corrected_Error': 'mean',
    'Relative_Error': 'mean',
    'Corrected_Relative_Error': 'mean'
}).reset_index()

# 按星期几顺序排序
weekday_order = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
weekday_error_comparison['weekday_order'] = weekday_error_comparison['weekday_name'].map({day: i for i, day in enumerate(weekday_order)})
weekday_error_comparison = weekday_error_comparison.sort_values('weekday_order').drop('weekday_order', axis=1)

# 创建星期几误差对比表格
weekday_error_data = []
for _, row in weekday_error_comparison.iterrows():
    weekday_error_data.append([
        row['weekday_name'],
        f"{row['Error']:.2f}",
        f"{row['Corrected_Error']:.2f}",
        f"{row['Relative_Error']:.2%}",
        f"{row['Corrected_Relative_Error']:.2%}"
    ])

fig, ax = plt.subplots(figsize=(12, 6))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('各星期校正前后误差对比', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=weekday_error_data,
                colLabels=['星期', '原始平均误差', '校正平均误差', '原始平均相对误差', '校正平均相对误差'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig(f'{corrected_plots_dir}/weekday_error_comparison_table.png', dpi=300, bbox_inches='tight')
print(f"\n各星期校正前后误差对比表格已保存到 {corrected_plots_dir}/weekday_error_comparison_table.png")

# 绘制各星期校正前后平均误差对比图
plt.figure(figsize=(12, 6))

# 绘制原始平均误差和校正后平均误差
x = np.arange(len(weekday_order))
width = 0.35

plt.bar(x - width/2, weekday_error_comparison['Error'], width, label='原始平均误差', color='blue', alpha=0.7)
plt.bar(x + width/2, weekday_error_comparison['Corrected_Error'], width, label='校正平均误差', color='green', alpha=0.7)

# 添加零线
plt.axhline(y=0, color='r', linestyle='-')

plt.title('各星期校正前后平均误差对比', fontsize=16)
plt.xlabel('星期', fontsize=12)
plt.ylabel('平均误差', fontsize=12)
plt.xticks(x, weekday_order)
plt.legend(fontsize=10)
plt.grid(True, axis='y')
plt.tight_layout()
plt.savefig(f'{corrected_plots_dir}/weekday_error_comparison.png')
print(f"\n各星期校正前后平均误差对比图已保存到 {corrected_plots_dir}/weekday_error_comparison.png")

print(f"\n所有校正结果和对比图表已保存到 {corrected_plots_dir} 目录")

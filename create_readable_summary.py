import pandas as pd
import numpy as np

def create_readable_summary():
    """
    创建更易读的预测结果汇总表
    """
    print("创建易读的预测结果汇总...")
    
    # 读取预测期数据
    pred_df = pd.read_csv('prediction_period_view.csv')
    
    # 创建简化的汇总表
    summary_data = []
    
    for _, row in pred_df.iterrows():
        date = row['ds']
        actual = row['真实票房']
        
        # 收集所有可用的预测
        predictions = {}
        if not pd.isna(row['7天预测']):
            predictions['7天预测'] = {
                'value': row['7天预测'],
                'error': row['7天预测误差'],
                'mape': row['7天相对误差(%)']
            }
        
        if not pd.isna(row['14天预测']):
            predictions['14天预测'] = {
                'value': row['14天预测'],
                'error': row['14天预测误差'],
                'mape': row['14天相对误差(%)']
            }
        
        if not pd.isna(row['21天预测']):
            predictions['21天预测'] = {
                'value': row['21天预测'],
                'error': row['21天预测误差'],
                'mape': row['21天相对误差(%)']
            }
        
        # 找出最佳预测（误差最小）
        best_pred = None
        min_error = float('inf')
        for pred_type, pred_data in predictions.items():
            if pred_data['error'] < min_error:
                min_error = pred_data['error']
                best_pred = pred_type
        
        # 构建汇总行
        summary_row = {
            '日期': date,
            '真实票房': f"{actual:.2f}",
            '7天预测': f"{predictions.get('7天预测', {}).get('value', 'N/A'):.2f}" if '7天预测' in predictions else 'N/A',
            '14天预测': f"{predictions.get('14天预测', {}).get('value', 'N/A'):.2f}" if '14天预测' in predictions else 'N/A',
            '21天预测': f"{predictions.get('21天预测', {}).get('value', 'N/A'):.2f}" if '21天预测' in predictions else 'N/A',
            '最佳预测': best_pred if best_pred else 'N/A',
            '最小误差': f"{min_error:.2f}" if min_error != float('inf') else 'N/A'
        }
        
        summary_data.append(summary_row)
    
    # 创建DataFrame
    summary_df = pd.DataFrame(summary_data)
    
    # 保存易读汇总
    readable_file = 'readable_prediction_summary.csv'
    summary_df.to_csv(readable_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 易读汇总已保存到: {readable_file}")
    
    # 打印预览
    print("\n📊 预测结果汇总预览:")
    print(summary_df.to_string(index=False))
    
    return summary_df

def create_performance_comparison():
    """
    创建性能对比表
    """
    print("\n创建性能对比表...")
    
    # 读取性能数据
    performance_df = pd.read_csv('enhanced_performance_summary.csv')
    
    # 创建更详细的对比
    comparison_data = []
    
    for _, row in performance_df.iterrows():
        days = row['预测天数']
        mae = row['MAE']
        rmse = row['RMSE']
        mape = row['MAPE(%)']
        quality = row['预测质量']
        
        comparison_data.append({
            '预测类型': f'{days}天预测',
            'MAE (平均绝对误差)': f'{mae:.2f}',
            'RMSE (均方根误差)': f'{rmse:.2f}',
            'MAPE (平均绝对百分比误差)': f'{mape:.2f}%',
            '预测质量评级': quality,
            '排名 (按MAE)': ''
        })
    
    # 按MAE排名
    comparison_df = pd.DataFrame(comparison_data)
    comparison_df = comparison_df.sort_values('MAE (平均绝对误差)')
    comparison_df['排名 (按MAE)'] = range(1, len(comparison_df) + 1)
    
    # 保存对比表
    comparison_file = 'performance_comparison.csv'
    comparison_df.to_csv(comparison_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 性能对比表已保存到: {comparison_file}")
    
    # 打印预览
    print("\n📈 性能对比表:")
    print(comparison_df.to_string(index=False))
    
    return comparison_df

def create_detailed_analysis():
    """
    创建详细分析报告
    """
    print("\n创建详细分析...")
    
    # 读取预测数据
    pred_df = pd.read_csv('prediction_period_view.csv')
    
    # 分析统计
    analysis = {
        '总预测天数': len(pred_df),
        '7天预测覆盖': (~pred_df['7天预测'].isna()).sum(),
        '14天预测覆盖': (~pred_df['14天预测'].isna()).sum(),
        '21天预测覆盖': (~pred_df['21天预测'].isna()).sum(),
        '重叠预测天数': (pred_df['数据类型'] == '重叠预测期').sum()
    }
    
    # 计算各预测方法的准确性
    accuracy_stats = {}
    
    for pred_type in ['7天预测', '14天预测', '21天预测']:
        mask = ~pred_df[pred_type].isna()
        if mask.any():
            errors = pred_df.loc[mask, f'{pred_type}误差']
            mapes = pred_df.loc[mask, f'{pred_type}相对误差(%)']
            
            accuracy_stats[pred_type] = {
                '样本数': mask.sum(),
                '平均误差': errors.mean(),
                '误差标准差': errors.std(),
                '平均MAPE': mapes.mean(),
                '最大误差': errors.max(),
                '最小误差': errors.min()
            }
    
    # 创建分析报告
    analysis_data = []
    
    # 基本统计
    analysis_data.append(['基本统计', '', '', '', '', ''])
    for key, value in analysis.items():
        analysis_data.append([key, str(value), '', '', '', ''])
    
    analysis_data.append(['', '', '', '', '', ''])
    analysis_data.append(['准确性统计', '样本数', '平均误差', '误差标准差', '平均MAPE(%)', '误差范围'])
    
    # 准确性统计
    for pred_type, stats in accuracy_stats.items():
        analysis_data.append([
            pred_type,
            str(stats['样本数']),
            f"{stats['平均误差']:.2f}",
            f"{stats['误差标准差']:.2f}",
            f"{stats['平均MAPE']:.2f}",
            f"{stats['最小误差']:.2f} - {stats['最大误差']:.2f}"
        ])
    
    # 保存分析报告
    analysis_df = pd.DataFrame(analysis_data, columns=['指标', '数值1', '数值2', '数值3', '数值4', '数值5'])
    analysis_file = 'detailed_analysis_report.csv'
    analysis_df.to_csv(analysis_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 详细分析报告已保存到: {analysis_file}")
    
    # 打印关键发现
    print("\n🔍 关键发现:")
    print(f"  • 总共预测了 {analysis['总预测天数']} 天的数据")
    print(f"  • 21天预测覆盖最广: {analysis['21天预测覆盖']} 天")
    print(f"  • 有 {analysis['重叠预测天数']} 天存在多个预测结果")
    
    best_method = min(accuracy_stats.keys(), key=lambda x: accuracy_stats[x]['平均误差'])
    print(f"  • 最准确的预测方法: {best_method} (平均误差: {accuracy_stats[best_method]['平均误差']:.2f})")
    
    return analysis_df

def main():
    """
    主函数
    """
    print("🚀 创建易读的预测结果汇总")
    print("="*50)
    
    # 1. 创建易读汇总
    summary_df = create_readable_summary()
    
    # 2. 创建性能对比
    comparison_df = create_performance_comparison()
    
    # 3. 创建详细分析
    analysis_df = create_detailed_analysis()
    
    print("\n" + "="*50)
    print("✅ 所有易读汇总文件已创建完成！")
    print("\n📁 新生成的文件:")
    print("  1. readable_prediction_summary.csv - 易读的预测结果汇总")
    print("  2. performance_comparison.csv - 性能对比表")
    print("  3. detailed_analysis_report.csv - 详细分析报告")
    
    print("\n💡 推荐查看顺序:")
    print("  1️⃣ readable_prediction_summary.csv - 快速了解预测效果")
    print("  2️⃣ performance_comparison.csv - 对比不同预测方法")
    print("  3️⃣ detailed_analysis_report.csv - 深入分析统计")

if __name__ == "__main__":
    main()

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from scipy.optimize import curve_fit
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建新的保存图表的目录
final_plots_dir = 'final_plots'
os.makedirs(final_plots_dir, exist_ok=True)
print(f"创建新的图表目录: {final_plots_dir}")

# 读取带有星期几信息的数据
data = pd.read_csv('xin_with_weekday.csv')

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])

print(f"数据行数: {len(data)}")

# 添加日期序号列（从1开始）
data['date_index'] = range(1, len(data) + 1)

# 添加标准周信息
def convert_date(date_str):
    try:
        return datetime.strptime(date_str, '%Y.%m.%d')
    except:
        return None

def get_week_number(date_obj):
    # 获取日期所在的ISO周数（周一为一周的开始）
    return date_obj.isocalendar()[1]

data['actual_date'] = data['Date'].apply(convert_date)
data['standard_week'] = data['actual_date'].apply(lambda x: get_week_number(x) if x else None)

# 绘制历史票房趋势
plt.figure(figsize=(14, 7))
plt.plot(data['date_index'], data['BOXOFFICE'], 'b-', label='历史票房')
plt.plot(data['date_index'], data['Max_BoxOffice'], 'g--', label='历史最大票房')
plt.plot(data['date_index'], data['Min_BoxOffice'], 'r--', label='历史最小票房')
plt.title('历史票房趋势', fontsize=16)
plt.xlabel('日期序号', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/historical_trend.png')
print(f"历史票房趋势图已保存到 {final_plots_dir}/historical_trend.png")

# 按周分组计算历史数据的统计信息
weekly_stats = data.groupby('standard_week').agg({
    'BOXOFFICE': ['min', 'max', 'mean'],
    'date_index': 'first',  # 使用每周的第一天的日期序号
    'Date': ['first', 'last']  # 获取每周的第一天和最后一天日期
}).reset_index()

# 重命名列
weekly_stats.columns = ['standard_week', 'min_boxoffice', 'max_boxoffice', 'mean_boxoffice', 'first_date_index', 'first_date', 'last_date']

# 定义拟合函数 - 使用指数衰减函数，适合票房数据的下降趋势
def exp_decay(x, a, b, c):
    return a * np.exp(-b * x) + c

# 定义多项式函数，可以捕捉更复杂的趋势
def poly_func(x, a, b, c, d):
    return a * x**3 + b * x**2 + c * x + d

# 定义对数函数，适合捕捉初期快速增长后逐渐稳定的趋势
def log_func(x, a, b, c):
    return a * np.log(b * x + 1) + c

# 使用最近的数据进行拟合（避免使用太旧的数据）
recent_weeks = 10  # 使用最近10周的数据
recent_weekly_stats = weekly_stats.tail(recent_weeks).copy()
recent_weekly_stats['week_index'] = range(1, len(recent_weekly_stats) + 1)  # 重新从1开始编号

# 准备拟合数据
x_data = recent_weekly_stats['week_index'].values
y_max_data = recent_weekly_stats['max_boxoffice'].values
y_min_data = recent_weekly_stats['min_boxoffice'].values
y_mean_data = recent_weekly_stats['mean_boxoffice'].values

# 尝试不同的拟合函数，选择最佳的一个
fit_functions = [
    (exp_decay, "指数衰减"),
    (log_func, "对数"),
    (poly_func, "多项式")
]

best_max_mse = float('inf')
best_min_mse = float('inf')
best_mean_mse = float('inf')
best_max_func = None
best_min_func = None
best_mean_func = None
best_max_params = None
best_min_params = None
best_mean_params = None
best_max_name = ""
best_min_name = ""
best_mean_name = ""

for func, name in fit_functions:
    try:
        # 尝试拟合最大票房
        max_params, _ = curve_fit(func, x_data, y_max_data, maxfev=10000)
        max_pred = func(x_data, *max_params)
        max_mse = np.mean((max_pred - y_max_data) ** 2)
        
        # 尝试拟合最小票房
        min_params, _ = curve_fit(func, x_data, y_min_data, maxfev=10000)
        min_pred = func(x_data, *min_params)
        min_mse = np.mean((min_pred - y_min_data) ** 2)
        
        # 尝试拟合平均票房
        mean_params, _ = curve_fit(func, x_data, y_mean_data, maxfev=10000)
        mean_pred = func(x_data, *mean_params)
        mean_mse = np.mean((mean_pred - y_mean_data) ** 2)
        
        print(f"\n{name}函数拟合结果:")
        print(f"最大票房MSE: {max_mse:.2f}")
        print(f"最小票房MSE: {min_mse:.2f}")
        print(f"平均票房MSE: {mean_mse:.2f}")
        
        # 更新最佳拟合函数
        if max_mse < best_max_mse:
            best_max_mse = max_mse
            best_max_func = func
            best_max_params = max_params
            best_max_name = name
            
        if min_mse < best_min_mse:
            best_min_mse = min_mse
            best_min_func = func
            best_min_params = min_params
            best_min_name = name
            
        if mean_mse < best_mean_mse:
            best_mean_mse = mean_mse
            best_mean_func = func
            best_mean_params = mean_params
            best_mean_name = name
            
    except Exception as e:
        print(f"{name}函数拟合失败: {str(e)}")

print(f"\n最佳拟合函数:")
print(f"最大票房: {best_max_name} (MSE: {best_max_mse:.2f})")
print(f"最小票房: {best_min_name} (MSE: {best_min_mse:.2f})")
print(f"平均票房: {best_mean_name} (MSE: {best_mean_mse:.2f})")

# 绘制拟合曲线
plt.figure(figsize=(14, 7))

# 绘制历史数据点
plt.scatter(x_data, y_max_data, color='green', label='历史周最大票房')
plt.scatter(x_data, y_min_data, color='red', label='历史周最小票房')
plt.scatter(x_data, y_mean_data, color='blue', label='历史周平均票房')

# 计算拟合曲线
x_fit = np.linspace(1, max(x_data) + 3, 100)  # 多预测3周
y_max_fit = best_max_func(x_fit, *best_max_params)
y_min_fit = best_min_func(x_fit, *best_min_params)
y_mean_fit = best_mean_func(x_fit, *best_mean_params)

# 绘制拟合曲线
plt.plot(x_fit, y_max_fit, 'g-', label=f'最大票房{best_max_name}拟合曲线')
plt.plot(x_fit, y_min_fit, 'r-', label=f'最小票房{best_min_name}拟合曲线')
plt.plot(x_fit, y_mean_fit, 'b-', label=f'平均票房{best_mean_name}拟合曲线')

plt.title('周票房拟合曲线', fontsize=16)
plt.xlabel('周序号', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/weekly_fit_curves.png')
print(f"周票房拟合曲线图已保存到 {final_plots_dir}/weekly_fit_curves.png")

# 定义需要预测的周
last_week = recent_weekly_stats['standard_week'].max()
future_weeks = [15, 16, 17]  # 预测第15、16、17周

# 创建未来周的预测数据框
future_weekly_df = pd.DataFrame({
    'standard_week': future_weeks,
    'week_index': range(max(x_data) + 1, max(x_data) + 1 + len(future_weeks))
})

# 预测未来周的票房值
future_weekly_df['Predicted_Max_BoxOffice'] = future_weekly_df['week_index'].apply(lambda x: best_max_func(x, *best_max_params))
future_weekly_df['Predicted_Min_BoxOffice'] = future_weekly_df['week_index'].apply(lambda x: best_min_func(x, *best_min_params))
future_weekly_df['Predicted_Mean_BoxOffice'] = future_weekly_df['week_index'].apply(lambda x: best_mean_func(x, *best_mean_params))

# 设置最小票房的下限，确保不会出现0或负值
min_boxoffice_floor = 100  # 至少为100
print(f"\n设置最小票房下限: {min_boxoffice_floor:.2f}")

# 确保预测值为正数且不小于下限
future_weekly_df['Predicted_Max_BoxOffice'] = future_weekly_df['Predicted_Max_BoxOffice'].apply(lambda x: max(min_boxoffice_floor, x))
future_weekly_df['Predicted_Min_BoxOffice'] = future_weekly_df['Predicted_Min_BoxOffice'].apply(lambda x: max(min_boxoffice_floor, x))
future_weekly_df['Predicted_Mean_BoxOffice'] = future_weekly_df['Predicted_Mean_BoxOffice'].apply(lambda x: max(min_boxoffice_floor, x))

# 确保最小值不大于最大值
for i, row in future_weekly_df.iterrows():
    if row['Predicted_Min_BoxOffice'] > row['Predicted_Max_BoxOffice']:
        future_weekly_df.at[i, 'Predicted_Min_BoxOffice'], future_weekly_df.at[i, 'Predicted_Max_BoxOffice'] = \
            future_weekly_df.at[i, 'Predicted_Max_BoxOffice'], future_weekly_df.at[i, 'Predicted_Min_BoxOffice']

# 打印预测结果
print("\n未来周的票房预测:")
print(future_weekly_df[['standard_week', 'Predicted_Min_BoxOffice', 'Predicted_Max_BoxOffice', 'Predicted_Mean_BoxOffice']])

# 保存预测结果
future_weekly_df.to_csv(f'{final_plots_dir}/weekly_predictions.csv', index=False)
print(f"\n预测结果已保存到 {final_plots_dir}/weekly_predictions.csv")

# 定义需要预测的日期范围
prediction_dates = [
    "2025.04.07", "2025.04.08", "2025.04.09", "2025.04.10", "2025.04.11", "2025.04.12", "2025.04.13",  # 第15周（标准周）
    "2025.04.14", "2025.04.15", "2025.04.16", "2025.04.17", "2025.04.18", "2025.04.19", "2025.04.20",  # 第16周（标准周）
    "2025.04.21"  # 第17周（标准周的第一天）
]

# 创建未来日期的预测数据框
future_df = pd.DataFrame({
    'Date': prediction_dates
})

# 添加日期和星期几信息
future_df['actual_date'] = future_df['Date'].apply(convert_date)
future_df['weekday'] = future_df['actual_date'].apply(lambda x: x.weekday() + 1 if x else None)
future_df['weekday_name'] = future_df['weekday'].apply(lambda x: ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][x] if x else '')
future_df['standard_week'] = future_df['actual_date'].apply(lambda x: get_week_number(x) if x else None)

# 添加日期序号列（从最后一个历史数据的序号开始）
last_date_index = data['date_index'].max()
future_df['date_index'] = range(last_date_index + 1, last_date_index + 1 + len(future_df))

# 为每个日期分配所在周的最大值和最小值
for i, row in future_df.iterrows():
    week = row['standard_week']
    week_data = future_weekly_df[future_weekly_df['standard_week'] == week]
    if len(week_data) > 0:
        future_df.at[i, 'Predicted_Min_BoxOffice'] = week_data['Predicted_Min_BoxOffice'].values[0]
        future_df.at[i, 'Predicted_Max_BoxOffice'] = week_data['Predicted_Max_BoxOffice'].values[0]
        future_df.at[i, 'Predicted_Mean_BoxOffice'] = week_data['Predicted_Mean_BoxOffice'].values[0]
    else:
        # 如果找不到对应的周，使用最后一周的预测值
        future_df.at[i, 'Predicted_Min_BoxOffice'] = future_weekly_df['Predicted_Min_BoxOffice'].iloc[-1]
        future_df.at[i, 'Predicted_Max_BoxOffice'] = future_weekly_df['Predicted_Max_BoxOffice'].iloc[-1]
        future_df.at[i, 'Predicted_Mean_BoxOffice'] = future_weekly_df['Predicted_Mean_BoxOffice'].iloc[-1]

# 计算每个星期几的票房比例（相对于周平均）
weekday_boxoffice_ratio = {}
for day in range(1, 8):
    day_data = data[data['weekday'] == day]['BOXOFFICE']
    if len(day_data) > 0:
        day_avg = day_data.mean()
        overall_avg = data['BOXOFFICE'].mean()
        weekday_boxoffice_ratio[day] = day_avg / overall_avg if overall_avg > 0 else 1.0
    else:
        weekday_boxoffice_ratio[day] = 1.0

# 根据星期几调整预测值
future_df['Weekday_Factor'] = future_df['weekday'].map(weekday_boxoffice_ratio)
future_df['Predicted_BoxOffice'] = future_df.apply(
    lambda row: row['Predicted_Min_BoxOffice'] + 
                (row['Predicted_Max_BoxOffice'] - row['Predicted_Min_BoxOffice']) * 
                (weekday_boxoffice_ratio.get(row['weekday'], 1.0) / max(weekday_boxoffice_ratio.values())),
    axis=1
)

# 打印预测结果
print("\n未来日期的票房预测:")
print(future_df[['Date', 'weekday_name', 'standard_week', 'Predicted_Min_BoxOffice', 'Predicted_Max_BoxOffice', 'Predicted_BoxOffice']])

# 保存预测结果
future_df.to_csv(f'{final_plots_dir}/daily_predictions.csv', index=False)
print(f"\n预测结果已保存到 {final_plots_dir}/daily_predictions.csv")

# 创建标准周预测表格
standard_weeks_data = []
for _, row in future_weekly_df.iterrows():
    week_num = row['standard_week']
    
    # 获取该周的日期范围
    week_dates = future_df[future_df['standard_week'] == week_num]
    if len(week_dates) > 0:
        start_date = week_dates['Date'].iloc[0]
        end_date = week_dates['Date'].iloc[-1]
        
        # 获取开始和结束日期的星期几
        start_date_obj = convert_date(start_date)
        end_date_obj = convert_date(end_date)
        start_weekday = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][start_date_obj.weekday() + 1]
        end_weekday = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][end_date_obj.weekday() + 1]
    else:
        start_date = f"第{week_num}周开始"
        end_date = f"第{week_num}周结束"
        start_weekday = ""
        end_weekday = ""
    
    min_val = row['Predicted_Min_BoxOffice']
    max_val = row['Predicted_Max_BoxOffice']
    mean_val = row['Predicted_Mean_BoxOffice']
    
    standard_weeks_data.append([
        f"第{week_num}周", 
        f"{start_date} - {end_date}", 
        f"{start_weekday} - {end_weekday}", 
        f"{min_val:.2f}", 
        f"{max_val:.2f}", 
        f"{mean_val:.2f}"
    ])

fig, ax = plt.subplots(figsize=(12, 6))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('标准周票房预测结果表', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=standard_weeks_data,
                colLabels=['周次', '日期范围', '星期范围', '最小值', '最大值', '预测平均值'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/weekly_prediction_table.png', dpi=300, bbox_inches='tight')
print(f"\n标准周预测表格已保存到 {final_plots_dir}/weekly_prediction_table.png")

# 绘制预测结果
plt.figure(figsize=(14, 7))

# 绘制最近的历史数据
plt.plot(data['date_index'], data['BOXOFFICE'], 'b-', label='历史票房')
plt.plot(data['date_index'], data['Max_BoxOffice'], 'g--', label='历史最大票房')
plt.plot(data['date_index'], data['Min_BoxOffice'], 'r--', label='历史最小票房')

# 绘制预测数据
pred_range = future_df['date_index'].values
plt.plot(pred_range, future_df['Predicted_BoxOffice'], 'b-o', label='预测票房')
plt.plot(pred_range, future_df['Predicted_Max_BoxOffice'], 'g-o', label='预测最大票房')
plt.plot(pred_range, future_df['Predicted_Min_BoxOffice'], 'r-o', label='预测最小票房')

# 为每个标准周创建区域
for week_num in future_df['standard_week'].unique():
    week_data = future_df[future_df['standard_week'] == week_num]
    if len(week_data) == 0:
        continue
        
    week_min = week_data['Predicted_Min_BoxOffice'].iloc[0]  # 所有天的最小值相同
    week_max = week_data['Predicted_Max_BoxOffice'].iloc[0]  # 所有天的最大值相同
    
    week_indices = week_data['date_index'].values
    
    plt.fill_between(
        week_indices,
        [week_min] * len(week_indices),
        [week_max] * len(week_indices),
        alpha=0.2,
        color=['blue', 'green', 'orange'][int(week_num) % 3],
        label=f'第{week_num}周范围' if week_num == future_df['standard_week'].unique()[0] else ""
    )

plt.title('未来票房预测', fontsize=16)
plt.xlabel('日期序号', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.tight_layout()
plt.savefig(f'{final_plots_dir}/future_predictions.png')
print(f"\n未来预测图已保存到 {final_plots_dir}/future_predictions.png")

print(f"\n所有预测图表已保存到 {final_plots_dir} 目录")

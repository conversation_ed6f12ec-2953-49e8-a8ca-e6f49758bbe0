import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 创建保存图表的目录
os.makedirs('plots_fixed', exist_ok=True)

# 读取数据
data = pd.read_csv('xin.csv')

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])

# 转换日期格式并计算实际的星期几
def convert_date(date_str):
    # 将格式为 "2025.01.29" 的字符串转换为日期对象
    try:
        return datetime.strptime(date_str, '%Y.%m.%d')
    except:
        # 如果转换失败，尝试其他格式
        try:
            return datetime.strptime(date_str, '%Y.%m.%d')
        except:
            return None

# 添加实际日期和星期几列
data['actual_date'] = data['Date'].apply(convert_date)
data['weekday'] = data['actual_date'].apply(lambda x: x.weekday() + 1 if x else None)  # 1-7 表示周一到周日
data['weekday_name'] = data['weekday'].apply(lambda x: ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][x] if x else '')

# 打印前10行数据，检查日期、星期几和权重
print("数据中的日期、星期几和权重:")
print(data[['Date', 'day', 'week', 'weekday', 'weekday_name']].head(10))

# 分析权重与票房的关系
plt.figure(figsize=(12, 6))
plt.scatter(data['week'], data['BOXOFFICE'], alpha=0.6)
plt.title('权重与票房的关系', fontsize=16)
plt.xlabel('权重 (week)', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.grid(True)
plt.tight_layout()
plt.savefig('plots_fixed/weight_boxoffice_relationship.png')
print("\n权重与票房关系图已保存到 plots_fixed/weight_boxoffice_relationship.png")

# 分析星期几与票房的关系
plt.figure(figsize=(12, 6))

# 按星期几分组计算平均票房
weekday_avg = data.groupby('weekday_name')['BOXOFFICE'].mean().reindex(['周一', '周二', '周三', '周四', '周五', '周六', '周日'])

# 绘制条形图
plt.bar(weekday_avg.index, weekday_avg.values, color='skyblue')
plt.title('各星期平均票房', fontsize=16)
plt.xlabel('星期', fontsize=12)
plt.ylabel('平均票房', fontsize=12)
plt.grid(True, axis='y')

# 添加数值标签
for i, v in enumerate(weekday_avg.values):
    plt.text(i, v + 100, f"{v:.2f}", ha='center')

plt.tight_layout()
plt.savefig('plots_fixed/weekday_average_boxoffice.png')
print("各星期平均票房图已保存到 plots_fixed/weekday_average_boxoffice.png")

# 分析权重与星期几的关系
plt.figure(figsize=(12, 6))

# 按星期几分组计算平均权重
weekday_weight_avg = data.groupby('weekday_name')['week'].mean().reindex(['周一', '周二', '周三', '周四', '周五', '周六', '周日'])

# 绘制条形图
plt.bar(weekday_weight_avg.index, weekday_weight_avg.values, color='lightgreen')
plt.title('各星期平均权重', fontsize=16)
plt.xlabel('星期', fontsize=12)
plt.ylabel('平均权重 (week)', fontsize=12)
plt.grid(True, axis='y')

# 添加数值标签
for i, v in enumerate(weekday_weight_avg.values):
    plt.text(i, v + 0.1, f"{v:.2f}", ha='center')

plt.tight_layout()
plt.savefig('plots_fixed/weekday_average_weight.png')
print("各星期平均权重图已保存到 plots_fixed/weekday_average_weight.png")

# 创建权重分布表格
weight_stats = data.groupby('weekday_name')['week'].agg(['min', 'max', 'mean', 'std']).reindex(['周一', '周二', '周三', '周四', '周五', '周六', '周日'])
weight_stats.columns = ['最小权重', '最大权重', '平均权重', '标准差']

# 创建表格数据
table_data = []
for weekday, row in weight_stats.iterrows():
    min_val = row['最小权重']
    max_val = row['最大权重']
    mean_val = row['平均权重']
    std_val = row['标准差']
    
    table_data.append([weekday, f"{min_val:.2f}", f"{max_val:.2f}", f"{mean_val:.2f}", f"{std_val:.2f}"])

# 创建表格
fig, ax = plt.subplots(figsize=(10, 6))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('各星期权重统计', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=table_data,
                colLabels=['星期', '最小权重', '最大权重', '平均权重', '标准差'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig('plots_fixed/weekday_weight_statistics.png', dpi=300, bbox_inches='tight')
print("\n各星期权重统计表已保存到 plots_fixed/weekday_weight_statistics.png")

# 创建未来两周的预测表格
future_weeks_data = [
    ["第15周", "2025.04.09 - 2025.04.15", "200.00", "320.00", "247.17"],
    ["第16周", "2025.04.16 - 2025.04.21", "190.00", "310.00", "251.23"]
]

fig, ax = plt.subplots(figsize=(10, 6))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('未来两周票房预测结果表', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=future_weeks_data,
                colLabels=['周次', '日期范围', '最小值', '最大值', '预测平均值'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig('plots_fixed/future_weeks_prediction.png', dpi=300, bbox_inches='tight')
print("未来两周预测表格已保存到 plots_fixed/future_weeks_prediction.png")

# 分析权重与票房的相关性
correlation = data['week'].corr(data['BOXOFFICE'])
print(f"\n权重与票房的相关系数: {correlation:.4f}")

# 创建一个散点图，按星期几着色
plt.figure(figsize=(12, 8))
for day in range(1, 8):
    day_data = data[data['weekday'] == day]
    plt.scatter(day_data['week'], day_data['BOXOFFICE'], 
                label=['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'][day],
                alpha=0.7)

plt.title('权重与票房的关系 (按星期几着色)', fontsize=16)
plt.xlabel('权重 (week)', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.savefig('plots_fixed/weight_boxoffice_by_weekday.png')
print("按星期几着色的权重与票房关系图已保存到 plots_fixed/weight_boxoffice_by_weekday.png")

print("\n所有分析图表已保存到 plots_fixed 目录")

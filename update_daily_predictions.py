import pandas as pd
import numpy as np

# 读取预测结果
daily_predictions = pd.read_csv('final_plots/daily_predictions.csv')
print(f"读取预测结果，共{len(daily_predictions)}行")

# 读取带有真实值和误差的预测结果
predictions_with_actual = pd.read_csv('final_plots/predictions_with_actual.csv')
print(f"读取带有真实值和误差的预测结果，共{len(predictions_with_actual)}行")

# 确保两个文件的日期匹配
if not all(daily_predictions['Date'] == predictions_with_actual['Date']):
    print("警告：两个文件的日期不匹配！")
else:
    print("两个文件的日期匹配")

# 将真实值和误差添加到daily_predictions.csv
daily_predictions['Actual_BoxOffice'] = predictions_with_actual['Actual_BoxOffice']
daily_predictions['Actual_Min_BoxOffice'] = predictions_with_actual['Actual_Min_BoxOffice']
daily_predictions['Actual_Max_BoxOffice'] = predictions_with_actual['Actual_Max_BoxOffice']
daily_predictions['Error'] = predictions_with_actual['Error']
daily_predictions['Error_Min'] = predictions_with_actual['Error_Min']
daily_predictions['Error_Max'] = predictions_with_actual['Error_Max']
daily_predictions['Relative_Error'] = predictions_with_actual['Relative_Error']
daily_predictions['Relative_Error_Min'] = predictions_with_actual['Relative_Error_Min']
daily_predictions['Relative_Error_Max'] = predictions_with_actual['Relative_Error_Max']

# 保存更新后的daily_predictions.csv
daily_predictions.to_csv('final_plots/daily_predictions.csv', index=False)
print(f"\n已将真实值和误差添加到 final_plots/daily_predictions.csv")

# 打印更新后的daily_predictions.csv的前几行
print("\n更新后的daily_predictions.csv的前几行:")
print(daily_predictions[['Date', 'weekday_name', 'Predicted_BoxOffice', 'Actual_BoxOffice', 'Error', 'Relative_Error']].head())

# 计算平均误差
mean_error = daily_predictions['Error'].mean()
mean_abs_error = daily_predictions['Error'].abs().mean()
mean_relative_error = daily_predictions['Relative_Error'].mean()
mean_relative_abs_error = daily_predictions['Relative_Error'].abs().mean()

print(f"\n平均误差: {mean_error:.2f}")
print(f"平均绝对误差: {mean_abs_error:.2f}")
print(f"平均相对误差: {mean_relative_error:.2%}")
print(f"平均相对绝对误差: {mean_relative_abs_error:.2%}")

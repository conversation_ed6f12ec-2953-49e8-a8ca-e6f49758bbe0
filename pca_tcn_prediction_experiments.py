import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import RobustScaler
import tensorflow as tf
from tensorflow.keras.layers import Dense, Conv1D, Dropout, LayerNormalization
from tensorflow.keras.layers import Input, Add, GlobalAveragePooling1D
from tensorflow.keras.layers import MultiHeadAttention, Concatenate
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping
import tensorflow.keras.backend as K
import os
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

# 复合损失函数：结合MAE和MAPE
def combined_loss(y_true, y_pred):
    """结合MAE和MAPE的复合损失函数"""
    epsilon = K.epsilon()
    mae = K.mean(K.abs(y_true - y_pred))
    mape = K.mean(K.abs((y_true - y_pred) / (K.abs(y_true) + epsilon))) * 100
    return 0.7 * mae + 0.3 * mape / 100

# 改进的TCN块
def create_improved_tcn_block(input_layer, filters, kernel_size, dilation_rate, dropout_rate=0.2):
    """改进的TCN块，包含标准化的残差连接"""
    conv1 = Conv1D(filters=filters, kernel_size=kernel_size, padding='causal',
                   dilation_rate=dilation_rate, use_bias=False)(input_layer)
    conv1 = LayerNormalization()(conv1)
    conv1 = tf.keras.layers.Activation('relu')(conv1)
    conv1 = Dropout(dropout_rate)(conv1)
    
    conv2 = Conv1D(filters=filters, kernel_size=kernel_size, padding='causal',
                   dilation_rate=dilation_rate, use_bias=False)(conv1)
    conv2 = LayerNormalization()(conv2)
    
    # 残差连接
    if input_layer.shape[-1] != filters:
        residual = Conv1D(filters=filters, kernel_size=1, padding='same', use_bias=False)(input_layer)
        residual = LayerNormalization()(residual)
    else:
        residual = input_layer
    
    output = Add()([conv2, residual])
    output = tf.keras.layers.Activation('relu')(output)
    output = Dropout(dropout_rate)(output)
    
    return output

# 多尺度TCN分支
def create_multiscale_tcn_branch(input_layer, filters, branch_name):
    """创建多尺度TCN分支"""
    if branch_name == "short":
        x = create_improved_tcn_block(input_layer, filters, 3, 1, 0.2)
        x = create_improved_tcn_block(x, filters, 3, 2, 0.2)
        x = create_improved_tcn_block(x, filters, 3, 4, 0.2)
    elif branch_name == "medium":
        x = create_improved_tcn_block(input_layer, filters, 5, 2, 0.2)
        x = create_improved_tcn_block(x, filters, 5, 4, 0.2)
        x = create_improved_tcn_block(x, filters, 5, 8, 0.2)
    elif branch_name == "long":
        x = create_improved_tcn_block(input_layer, filters, 7, 4, 0.2)
        x = create_improved_tcn_block(x, filters, 7, 8, 0.2)
        x = create_improved_tcn_block(x, filters, 7, 16, 0.2)
    return x

# 自注意力机制
def create_attention_layer(input_layer, num_heads=4):
    """创建多头自注意力层"""
    attention_output = MultiHeadAttention(
        num_heads=num_heads,
        key_dim=input_layer.shape[-1] // num_heads,
        dropout=0.1
    )(input_layer, input_layer)
    
    attention_output = Add()([input_layer, attention_output])
    attention_output = LayerNormalization()(attention_output)
    return attention_output

# 构建改进的TCN模型
def build_improved_tcn_model(input_shape):
    """构建改进的多尺度TCN模型"""
    input_layer = Input(shape=input_shape)
    
    # 多尺度TCN分支
    short_branch = create_multiscale_tcn_branch(input_layer, filters=32, branch_name="short")
    medium_branch = create_multiscale_tcn_branch(input_layer, filters=32, branch_name="medium") 
    long_branch = create_multiscale_tcn_branch(input_layer, filters=32, branch_name="long")
    
    # 合并多尺度特征
    merged_features = Concatenate(axis=-1)([short_branch, medium_branch, long_branch])
    
    # 应用注意力机制
    attention_output = create_attention_layer(merged_features, num_heads=4)
    
    # 额外的TCN层
    x = create_improved_tcn_block(attention_output, filters=64, kernel_size=3, dilation_rate=1, dropout_rate=0.2)
    x = create_improved_tcn_block(x, filters=64, kernel_size=3, dilation_rate=2, dropout_rate=0.2)
    
    # 全局平均池化
    x = GlobalAveragePooling1D()(x)
    
    # 密集层
    x = Dense(64, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.0001))(x)
    x = Dropout(0.2)(x)
    x = Dense(32, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.0001))(x)
    x = Dropout(0.2)(x)
    
    # 输出层
    output = Dense(1, activation='linear', name='box_office')(x)
    
    model = Model(inputs=input_layer, outputs=output)
    model.compile(
        optimizer=Adam(learning_rate=0.0005, beta_1=0.9, beta_2=0.999),
        loss=combined_loss,
        metrics=['mae', 'mape']
    )
    
    return model

# 创建序列数据
def create_sequences(X, y, seq_length):
    """创建时间序列数据"""
    X_seq, y_seq = [], []
    for i in range(len(X) - seq_length):
        X_seq.append(X[i:i+seq_length])
        y_seq.append(y[i+seq_length])
    return np.array(X_seq), np.array(y_seq)

# 预测实验函数
def run_prediction_experiment(data, days_to_remove, seq_length=14):
    """运行预测实验"""
    print(f"\n{'='*60}")
    print(f"实验: 删除最后{days_to_remove}天，预测未来{days_to_remove}天")
    print(f"{'='*60}")
    
    # 准备数据
    total_rows = len(data)
    train_data = data.iloc[:-days_to_remove].copy()
    test_data = data.iloc[-days_to_remove:].copy()
    
    print(f"原始数据: {total_rows}行")
    print(f"训练数据: {len(train_data)}行")
    print(f"测试数据: {len(test_data)}行")
    
    # 特征和目标
    features = ['PCA1', 'PCA2', 'PCA3', 'PCA4', 'is_weekend', 'is_holiday']
    target = 'y'
    
    # 数据标准化
    feature_scaler = RobustScaler()
    target_scaler = RobustScaler()
    
    X_train = feature_scaler.fit_transform(train_data[features])
    y_train = target_scaler.fit_transform(train_data[[target]])
    
    X_test = feature_scaler.transform(test_data[features])
    y_test = test_data[target].values
    
    # 创建序列
    X_seq, y_seq = create_sequences(X_train, y_train, seq_length)
    
    if len(X_seq) == 0:
        print(f"警告: 序列长度{seq_length}太长，训练数据不足")
        return None
    
    print(f"序列数据形状: X={X_seq.shape}, y={y_seq.shape}")
    
    # 构建和训练模型
    input_shape = (X_seq.shape[1], X_seq.shape[2])
    model = build_improved_tcn_model(input_shape)
    
    # 训练回调
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=30,
        restore_best_weights=True,
        min_delta=0.0001
    )
    
    reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.7,
        patience=15,
        min_lr=0.00001,
        verbose=0
    )
    
    print("开始训练模型...")
    history = model.fit(
        X_seq, y_seq,
        epochs=100,
        batch_size=16,
        validation_split=0.25,
        callbacks=[early_stopping, reduce_lr],
        verbose=0
    )
    
    print(f"训练完成，共{len(history.history['loss'])}轮")
    
    # 预测
    print("开始预测...")
    last_sequence = X_train[-seq_length:].reshape(1, seq_length, X_train.shape[1])
    
    predictions = []
    current_seq = last_sequence.copy()
    
    for i in range(days_to_remove):
        pred = model.predict(current_seq, verbose=0)
        pred_original = target_scaler.inverse_transform(pred)[0, 0]
        predictions.append(pred_original)
        
        # 更新序列
        if i < days_to_remove - 1:
            current_seq = np.append(current_seq[:, 1:, :],
                                   X_test[i].reshape(1, 1, X_test.shape[1]),
                                   axis=1)
    
    predictions = np.array(predictions)
    
    # 计算评估指标
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    
    mae = mean_absolute_error(y_test, predictions)
    rmse = np.sqrt(mean_squared_error(y_test, predictions))
    mape = np.mean(np.abs((y_test - predictions) / y_test)) * 100
    
    print(f"\n预测性能:")
    print(f"MAE: {mae:.2f}")
    print(f"RMSE: {rmse:.2f}")
    print(f"MAPE: {mape:.2f}%")
    
    # 返回结果
    results = {
        'days_removed': days_to_remove,
        'predictions': predictions,
        'actual': y_test,
        'dates': test_data['ds'].values,
        'mae': mae,
        'rmse': rmse,
        'mape': mape,
        'history': history,
        'model': model
    }
    
    return results

# 主函数
def main():
    # 加载数据
    print("加载PCA4_NeZha.csv数据...")
    data = pd.read_csv('PCA4_NeZha.csv')
    
    print(f"数据形状: {data.shape}")
    print(f"列名: {list(data.columns)}")
    print(f"数据范围: {data['ds'].iloc[0]} 到 {data['ds'].iloc[-1]}")
    print(f"票房范围: {data['y'].min():.2f} 到 {data['y'].max():.2f}")
    
    # 运行三个实验
    experiments = [7, 14, 21]
    results = {}
    
    for days in experiments:
        result = run_prediction_experiment(data, days)
        if result:
            results[days] = result
    
    # 创建结果目录
    os.makedirs('pca_experiment_results', exist_ok=True)

    # 可视化结果
    visualize_results(results, data)

    # 保存结果
    save_results(results)

    return results

def visualize_results(results, original_data):
    """可视化实验结果"""
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. 预测vs实际对比图
    fig, axes = plt.subplots(2, 2, figsize=(20, 15))

    # 性能对比
    ax1 = axes[0, 0]
    days_list = list(results.keys())
    mae_list = [results[d]['mae'] for d in days_list]
    rmse_list = [results[d]['rmse'] for d in days_list]
    mape_list = [results[d]['mape'] for d in days_list]

    x = np.arange(len(days_list))
    width = 0.25

    ax1.bar(x - width, mae_list, width, label='MAE', alpha=0.8)
    ax1.bar(x, rmse_list, width, label='RMSE', alpha=0.8)
    ax1.bar(x + width, mape_list, width, label='MAPE', alpha=0.8)

    ax1.set_title('不同预测天数的性能对比', fontsize=16, fontweight='bold')
    ax1.set_xlabel('预测天数')
    ax1.set_ylabel('误差值')
    ax1.set_xticks(x)
    ax1.set_xticklabels([f'{d}天' for d in days_list])
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 预测结果对比
    ax2 = axes[0, 1]
    colors = ['blue', 'red', 'green']
    for i, days in enumerate(days_list):
        result = results[days]
        ax2.plot(range(days), result['actual'], 'o-',
                label=f'实际值({days}天)', color=colors[i], alpha=0.7)
        ax2.plot(range(days), result['predictions'], 's--',
                label=f'预测值({days}天)', color=colors[i], alpha=0.7)

    ax2.set_title('预测值vs实际值对比', fontsize=16, fontweight='bold')
    ax2.set_xlabel('天数')
    ax2.set_ylabel('票房')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 误差分析
    ax3 = axes[1, 0]
    for i, days in enumerate(days_list):
        result = results[days]
        errors = result['actual'] - result['predictions']
        ax3.hist(errors, bins=10, alpha=0.6, label=f'{days}天预测误差', color=colors[i])

    ax3.set_title('预测误差分布', fontsize=16, fontweight='bold')
    ax3.set_xlabel('误差值')
    ax3.set_ylabel('频次')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 时间序列趋势
    ax4 = axes[1, 1]
    # 显示原始数据的最后30天
    last_30_days = original_data.tail(30)
    ax4.plot(range(30), last_30_days['y'].values, 'k-', linewidth=2, label='历史数据', alpha=0.8)

    # 添加预测结果
    start_idx = 30
    for i, days in enumerate(days_list):
        result = results[days]
        pred_range = range(start_idx - days, start_idx)
        ax4.plot(pred_range, result['predictions'], 'o--',
                label=f'{days}天预测', color=colors[i], linewidth=2)
        ax4.plot(pred_range, result['actual'], 's-',
                label=f'{days}天实际', color=colors[i], alpha=0.6)

    ax4.set_title('时间序列预测趋势', fontsize=16, fontweight='bold')
    ax4.set_xlabel('时间点')
    ax4.set_ylabel('票房')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('pca_experiment_results/experiment_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 详细的单独预测图
    for days in results.keys():
        plt.figure(figsize=(15, 8))
        result = results[days]

        # 绘制预测vs实际
        x_range = range(len(result['predictions']))
        plt.plot(x_range, result['actual'], 'bo-', linewidth=2, markersize=8,
                label='实际票房', alpha=0.8)
        plt.plot(x_range, result['predictions'], 'rs--', linewidth=2, markersize=8,
                label='预测票房', alpha=0.8)

        # 添加误差带
        errors = np.abs(result['actual'] - result['predictions'])
        plt.fill_between(x_range, result['predictions'] - errors,
                        result['predictions'] + errors, alpha=0.2, color='red')

        plt.title(f'{days}天预测结果详细分析\nMAE: {result["mae"]:.2f}, RMSE: {result["rmse"]:.2f}, MAPE: {result["mape"]:.2f}%',
                 fontsize=16, fontweight='bold')
        plt.xlabel('天数')
        plt.ylabel('票房')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)

        # 添加日期标签
        if len(result['dates']) <= 21:  # 只有当天数不太多时才显示日期
            plt.xticks(x_range, result['dates'], rotation=45)

        plt.tight_layout()
        plt.savefig(f'pca_experiment_results/prediction_{days}days.png', dpi=300, bbox_inches='tight')
        plt.show()

def save_results(results):
    """保存实验结果"""
    # 保存性能汇总
    summary_data = []
    for days, result in results.items():
        summary_data.append({
            '预测天数': days,
            'MAE': result['mae'],
            'RMSE': result['rmse'],
            'MAPE(%)': result['mape']
        })

    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('pca_experiment_results/performance_summary.csv', index=False, encoding='utf-8-sig')

    # 保存详细预测结果
    for days, result in results.items():
        detail_data = pd.DataFrame({
            '日期': result['dates'],
            '实际票房': result['actual'],
            '预测票房': result['predictions'],
            '绝对误差': np.abs(result['actual'] - result['predictions']),
            '相对误差(%)': np.abs((result['actual'] - result['predictions']) / result['actual']) * 100
        })
        detail_data.to_csv(f'pca_experiment_results/detailed_results_{days}days.csv',
                          index=False, encoding='utf-8-sig')

    print(f"\n结果已保存:")
    print(f"- 性能汇总: pca_experiment_results/performance_summary.csv")
    for days in results.keys():
        print(f"- {days}天详细结果: pca_experiment_results/detailed_results_{days}days.csv")

if __name__ == "__main__":
    results = main()
    print(f"\n实验完成！结果保存在 'pca_experiment_results' 目录")

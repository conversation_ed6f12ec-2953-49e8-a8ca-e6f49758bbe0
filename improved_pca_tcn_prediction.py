import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import RobustScaler, StandardScaler
import tensorflow as tf
from tensorflow.keras.layers import Dense, Conv1D, Dropout, LayerNormalization
from tensorflow.keras.layers import Input, Add, GlobalAveragePooling1D
from tensorflow.keras.layers import MultiHeadAttention, Concatenate
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping
import tensorflow.keras.backend as K
import os
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

# 改进的损失函数
def improved_combined_loss(y_true, y_pred):
    """改进的复合损失函数，增加对小值的保护"""
    epsilon = K.epsilon()
    
    # MAE损失
    mae = K.mean(K.abs(y_true - y_pred))
    
    # 改进的MAPE，对小值更友好
    relative_error = K.abs((y_true - y_pred) / (K.abs(y_true) + epsilon))
    # 使用log(1+x)来减少极端值的影响
    mape = K.mean(K.log(1 + relative_error)) * 100
    
    # Huber损失，对异常值更鲁棒
    huber_delta = 1.0
    huber_loss = tf.where(
        K.abs(y_true - y_pred) <= huber_delta,
        0.5 * K.square(y_true - y_pred),
        huber_delta * (K.abs(y_true - y_pred) - 0.5 * huber_delta)
    )
    huber = K.mean(huber_loss)
    
    # 组合损失：50% MAE + 30% Huber + 20% 改进MAPE
    return 0.5 * mae + 0.3 * huber + 0.2 * mape / 100

# 改进的TCN块
def create_improved_tcn_block(input_layer, filters, kernel_size, dilation_rate, dropout_rate=0.2):
    """改进的TCN块，增加更多正则化"""
    conv1 = Conv1D(filters=filters, kernel_size=kernel_size, padding='causal',
                   dilation_rate=dilation_rate, use_bias=False,
                   kernel_regularizer=tf.keras.regularizers.l2(0.001))(input_layer)
    conv1 = LayerNormalization()(conv1)
    conv1 = tf.keras.layers.Activation('relu')(conv1)
    conv1 = Dropout(dropout_rate)(conv1)
    
    conv2 = Conv1D(filters=filters, kernel_size=kernel_size, padding='causal',
                   dilation_rate=dilation_rate, use_bias=False,
                   kernel_regularizer=tf.keras.regularizers.l2(0.001))(conv1)
    conv2 = LayerNormalization()(conv2)
    
    # 残差连接
    if input_layer.shape[-1] != filters:
        residual = Conv1D(filters=filters, kernel_size=1, padding='same', use_bias=False)(input_layer)
        residual = LayerNormalization()(residual)
    else:
        residual = input_layer
    
    output = Add()([conv2, residual])
    output = tf.keras.layers.Activation('relu')(output)
    output = Dropout(dropout_rate)(output)
    
    return output

# 多尺度TCN分支（减少复杂度）
def create_multiscale_tcn_branch(input_layer, filters, branch_name):
    """创建多尺度TCN分支，减少过拟合"""
    if branch_name == "short":
        x = create_improved_tcn_block(input_layer, filters, 3, 1, 0.3)
        x = create_improved_tcn_block(x, filters, 3, 2, 0.3)
    elif branch_name == "medium":
        x = create_improved_tcn_block(input_layer, filters, 5, 2, 0.3)
        x = create_improved_tcn_block(x, filters, 5, 4, 0.3)
    elif branch_name == "long":
        x = create_improved_tcn_block(input_layer, filters, 7, 4, 0.3)
        x = create_improved_tcn_block(x, filters, 7, 8, 0.3)
    return x

# 自注意力机制
def create_attention_layer(input_layer, num_heads=2):
    """创建多头自注意力层，减少头数"""
    attention_output = MultiHeadAttention(
        num_heads=num_heads,
        key_dim=input_layer.shape[-1] // num_heads,
        dropout=0.2
    )(input_layer, input_layer)
    
    attention_output = Add()([input_layer, attention_output])
    attention_output = LayerNormalization()(attention_output)
    return attention_output

# 构建改进的TCN模型
def build_improved_tcn_model(input_shape):
    """构建改进的多尺度TCN模型，减少过拟合"""
    input_layer = Input(shape=input_shape)
    
    # 多尺度TCN分支（减少滤波器数量）
    short_branch = create_multiscale_tcn_branch(input_layer, filters=16, branch_name="short")
    medium_branch = create_multiscale_tcn_branch(input_layer, filters=16, branch_name="medium") 
    long_branch = create_multiscale_tcn_branch(input_layer, filters=16, branch_name="long")
    
    # 合并多尺度特征
    merged_features = Concatenate(axis=-1)([short_branch, medium_branch, long_branch])
    
    # 应用注意力机制
    attention_output = create_attention_layer(merged_features, num_heads=2)
    
    # 额外的TCN层（减少复杂度）
    x = create_improved_tcn_block(attention_output, filters=32, kernel_size=3, dilation_rate=1, dropout_rate=0.3)
    
    # 全局平均池化
    x = GlobalAveragePooling1D()(x)
    
    # 密集层（减少参数）
    x = Dense(32, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))(x)
    x = Dropout(0.4)(x)
    x = Dense(16, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))(x)
    x = Dropout(0.4)(x)
    
    # 输出层，添加约束
    output = Dense(1, activation='linear', name='box_office',
                  kernel_regularizer=tf.keras.regularizers.l2(0.01))(x)
    
    model = Model(inputs=input_layer, outputs=output)
    model.compile(
        optimizer=Adam(learning_rate=0.0001, beta_1=0.9, beta_2=0.999),  # 更小的学习率
        loss=improved_combined_loss,
        metrics=['mae', 'mape']
    )
    
    return model

# 改进的数据预处理
def improved_data_preprocessing(data, target_col='y'):
    """改进的数据预处理，包括对数变换"""
    processed_data = data.copy()
    
    # 对目标变量进行对数变换
    processed_data[f'{target_col}_log'] = np.log1p(processed_data[target_col])
    
    # 检查异常值
    Q1 = processed_data[target_col].quantile(0.25)
    Q3 = processed_data[target_col].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    
    outliers = processed_data[(processed_data[target_col] < lower_bound) | 
                             (processed_data[target_col] > upper_bound)]
    
    print(f"检测到 {len(outliers)} 个异常值")
    print(f"异常值范围: {outliers[target_col].min():.2f} - {outliers[target_col].max():.2f}")
    
    return processed_data

# 创建序列数据
def create_sequences(X, y, seq_length):
    """创建时间序列数据"""
    X_seq, y_seq = [], []
    for i in range(len(X) - seq_length):
        X_seq.append(X[i:i+seq_length])
        y_seq.append(y[i+seq_length])
    return np.array(X_seq), np.array(y_seq)

# 改进的预测实验函数
def run_improved_prediction_experiment(data, days_to_remove, seq_length=10):
    """运行改进的预测实验"""
    print(f"\n{'='*60}")
    print(f"改进实验: 删除最后{days_to_remove}天，预测未来{days_to_remove}天")
    print(f"{'='*60}")
    
    # 改进的数据预处理
    processed_data = improved_data_preprocessing(data)
    
    # 准备数据
    train_data = processed_data.iloc[:-days_to_remove].copy()
    test_data = processed_data.iloc[-days_to_remove:].copy()
    
    print(f"训练数据: {len(train_data)}行")
    print(f"测试数据: {len(test_data)}行")
    
    # 特征和目标
    features = ['PCA1', 'PCA2', 'PCA3', 'PCA4', 'is_weekend', 'is_holiday']
    target = 'y_log'  # 使用对数变换后的目标
    
    # 数据标准化
    feature_scaler = StandardScaler()  # 使用StandardScaler
    target_scaler = StandardScaler()
    
    X_train = feature_scaler.fit_transform(train_data[features])
    y_train = target_scaler.fit_transform(train_data[[target]])
    
    X_test = feature_scaler.transform(test_data[features])
    y_test = test_data['y'].values  # 原始值用于评估
    
    # 创建序列
    X_seq, y_seq = create_sequences(X_train, y_train, seq_length)
    
    print(f"序列数据形状: X={X_seq.shape}, y={y_seq.shape}")
    
    # 构建和训练模型
    input_shape = (X_seq.shape[1], X_seq.shape[2])
    model = build_improved_tcn_model(input_shape)
    
    print(f"模型参数数量: {model.count_params():,}")
    
    # 训练回调
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=50,
        restore_best_weights=True,
        min_delta=0.0001
    )
    
    reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=20,
        min_lr=0.000001,
        verbose=0
    )
    
    print("开始训练改进模型...")
    history = model.fit(
        X_seq, y_seq,
        epochs=200,
        batch_size=32,  # 增加batch size
        validation_split=0.3,  # 增加验证集
        callbacks=[early_stopping, reduce_lr],
        verbose=0
    )
    
    print(f"训练完成，共{len(history.history['loss'])}轮")
    
    # 预测
    print("开始预测...")
    last_sequence = X_train[-seq_length:].reshape(1, seq_length, X_train.shape[1])
    
    predictions_log = []
    current_seq = last_sequence.copy()
    
    for i in range(days_to_remove):
        pred_log = model.predict(current_seq, verbose=0)
        pred_log_original = target_scaler.inverse_transform(pred_log)[0, 0]
        predictions_log.append(pred_log_original)
        
        # 更新序列
        if i < days_to_remove - 1:
            current_seq = np.append(current_seq[:, 1:, :],
                                   X_test[i].reshape(1, 1, X_test.shape[1]),
                                   axis=1)
    
    # 反对数变换
    predictions = np.expm1(predictions_log)  # 反对数变换
    
    # 后处理：确保预测值在合理范围内
    predictions = np.clip(predictions, 0, np.max(data['y']) * 2)
    
    # 计算评估指标
    from sklearn.metrics import mean_absolute_error, mean_squared_error
    
    mae = mean_absolute_error(y_test, predictions)
    rmse = np.sqrt(mean_squared_error(y_test, predictions))
    mape = np.mean(np.abs((y_test - predictions) / y_test)) * 100
    
    print(f"\n改进模型预测性能:")
    print(f"MAE: {mae:.2f}")
    print(f"RMSE: {rmse:.2f}")
    print(f"MAPE: {mape:.2f}%")
    
    return {
        'days_removed': days_to_remove,
        'predictions': predictions,
        'actual': y_test,
        'dates': test_data['ds'].values,
        'mae': mae,
        'rmse': rmse,
        'mape': mape,
        'history': history,
        'model': model
    }

# 主函数
def main():
    # 加载数据
    print("加载PCA4_NeZha.csv数据...")
    data = pd.read_csv('PCA4_NeZha.csv')
    
    print(f"数据形状: {data.shape}")
    print(f"票房统计: 均值={data['y'].mean():.2f}, 标准差={data['y'].std():.2f}")
    print(f"票房范围: {data['y'].min():.2f} 到 {data['y'].max():.2f}")
    
    # 运行改进的实验
    experiments = [7, 14, 21]
    improved_results = {}
    
    for days in experiments:
        result = run_improved_prediction_experiment(data, days)
        if result:
            improved_results[days] = result
    
    # 创建结果目录
    os.makedirs('improved_pca_results', exist_ok=True)
    
    return improved_results

if __name__ == "__main__":
    improved_results = main()
    print(f"\n改进实验完成！")

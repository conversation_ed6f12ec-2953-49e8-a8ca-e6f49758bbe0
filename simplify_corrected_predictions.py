import pandas as pd

# 读取校正后的预测结果
corrected_predictions = pd.read_csv('corrected_plots/corrected_predictions.csv')
print(f"读取校正后的预测结果，共{len(corrected_predictions)}行")

# 只保留需要的列
simplified_predictions = corrected_predictions[['Date', 'weekday_name', 'Corrected_BoxOffice', 'Actual_BoxOffice', 'Corrected_Error']]

# 重命名列，使其更加直观
simplified_predictions.columns = ['日期', '星期', '校正预测值', '真实值', '误差(真实值-预测值)']

# 保存简化后的预测结果
simplified_predictions.to_csv('corrected_plots/simplified_corrected_predictions.csv', index=False)
print(f"\n已将简化后的校正预测结果保存到 corrected_plots/simplified_corrected_predictions.csv")

# 打印简化后的预测结果的前几行
print("\n简化后的校正预测结果的前几行:")
print(simplified_predictions.head())

# 计算平均误差
mean_error = simplified_predictions['误差(真实值-预测值)'].mean()
mean_abs_error = simplified_predictions['误差(真实值-预测值)'].abs().mean()
mean_relative_error = (simplified_predictions['误差(真实值-预测值)'] / simplified_predictions['真实值']).mean()
mean_relative_abs_error = (simplified_predictions['误差(真实值-预测值)'] / simplified_predictions['真实值']).abs().mean()

print(f"\n平均误差: {mean_error:.2f}")
print(f"平均绝对误差: {mean_abs_error:.2f}")
print(f"平均相对误差: {mean_relative_error:.2%}")
print(f"平均相对绝对误差: {mean_relative_abs_error:.2%}")

# 更新原始的corrected_predictions.csv文件，只保留需要的列
corrected_predictions = simplified_predictions
corrected_predictions.to_csv('corrected_plots/corrected_predictions.csv', index=False)
print(f"\n已更新 corrected_plots/corrected_predictions.csv，只保留需要的列")

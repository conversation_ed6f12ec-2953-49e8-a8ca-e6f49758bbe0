import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def post_process_original_predictions():
    """
    对原始模型预测结果进行后处理，消除负值
    """
    print("🚀 使用推荐方案：原始模型 + 后处理")
    print("="*60)
    
    try:
        # 读取原始预测结果
        original_df = pd.read_csv('simple_prediction_summary.csv')
        print(f"✅ 成功读取原始预测结果: {len(original_df)}行")
        
        # 创建后处理版本
        processed_df = original_df.copy()
        
        # 统计负值情况
        negative_stats = {}
        
        # 处理每个预测列
        prediction_columns = ['7天预测', '14天预测', '21天预测']
        
        for col in prediction_columns:
            if col in processed_df.columns:
                print(f"\n📊 处理 {col}:")
                
                # 转换为数值，处理'-'符号
                numeric_values = pd.to_numeric(processed_df[col], errors='coerce')
                
                # 统计负值
                negative_mask = numeric_values < 0
                negative_count = negative_mask.sum()
                negative_values = numeric_values[negative_mask].tolist()
                
                negative_stats[col] = {
                    'count': negative_count,
                    'values': negative_values,
                    'min_value': numeric_values.min() if not numeric_values.isna().all() else None
                }
                
                print(f"   • 负值数量: {negative_count}")
                if negative_count > 0:
                    print(f"   • 负值范围: {min(negative_values):.1f} 到 {max(negative_values):.1f}")
                    print(f"   • 负值列表: {[f'{v:.1f}' for v in negative_values]}")
                
                # 后处理：将负值设为0
                processed_values = numeric_values.copy()
                processed_values[negative_mask] = 0
                
                # 更新数据框
                processed_df[col] = processed_values.apply(lambda x: f"{x:.1f}" if not pd.isna(x) else "-")
                
                print(f"   ✅ 已将 {negative_count} 个负值设为 0")
        
        # 重新计算误差
        print(f"\n🔧 重新计算预测误差...")
        
        for pred_col in prediction_columns:
            error_col = pred_col.replace('预测', '误差')
            
            if pred_col in processed_df.columns and error_col in processed_df.columns:
                # 计算新的误差
                for idx, row in processed_df.iterrows():
                    actual = row['真实票房']
                    pred_str = row[pred_col]
                    
                    if pred_str != "-" and not pd.isna(actual):
                        pred_val = float(pred_str)
                        new_error = abs(actual - pred_val)
                        processed_df.loc[idx, error_col] = f"{new_error:.1f}"
        
        # 保存后处理结果
        output_file = 'corrected_prediction_summary.csv'
        processed_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n✅ 后处理完成！结果已保存到: {output_file}")
        
        # 显示处理前后对比
        show_before_after_comparison(original_df, processed_df, negative_stats)
        
        # 计算性能改进
        calculate_performance_improvement(processed_df)
        
        return processed_df, negative_stats
        
    except FileNotFoundError:
        print("❌ 未找到原始预测结果文件 'simple_prediction_summary.csv'")
        return None, None

def show_before_after_comparison(original_df, processed_df, negative_stats):
    """
    显示处理前后的对比
    """
    print(f"\n📋 处理前后对比")
    print("="*60)
    
    # 找出有负值的行进行对比
    comparison_data = []
    
    for idx, row in original_df.iterrows():
        has_negative = False
        comparison_row = {
            '日期': row['日期'],
            '真实票房': row['真实票房']
        }
        
        for pred_col in ['7天预测', '14天预测', '21天预测']:
            if pred_col in row and row[pred_col] != "-":
                try:
                    original_val = float(row[pred_col])
                    processed_val = float(processed_df.loc[idx, pred_col])
                    
                    if original_val < 0:
                        has_negative = True
                        comparison_row[f'{pred_col}_原始'] = f"{original_val:.1f}"
                        comparison_row[f'{pred_col}_处理后'] = f"{processed_val:.1f}"
                    else:
                        comparison_row[f'{pred_col}_原始'] = f"{original_val:.1f}"
                        comparison_row[f'{pred_col}_处理后'] = f"{processed_val:.1f}"
                except:
                    pass
        
        if has_negative:
            comparison_data.append(comparison_row)
    
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        print("🔍 有负值修正的案例:")
        print(comparison_df.to_string(index=False))
        
        # 保存对比结果
        comparison_df.to_csv('before_after_comparison.csv', index=False, encoding='utf-8-sig')
        print(f"\n💾 详细对比已保存到: before_after_comparison.csv")
    else:
        print("ℹ️  未发现需要修正的负值")

def calculate_performance_improvement(processed_df):
    """
    计算后处理后的性能指标
    """
    print(f"\n📈 后处理后的性能分析")
    print("="*60)
    
    performance_data = []
    
    for pred_type in ['7天预测', '14天预测', '21天预测']:
        error_col = pred_type.replace('预测', '误差')
        
        if pred_type in processed_df.columns and error_col in processed_df.columns:
            # 提取有效的预测和误差数据
            valid_mask = (processed_df[pred_type] != "-") & (processed_df[error_col] != "-")
            
            if valid_mask.any():
                valid_data = processed_df[valid_mask]
                
                # 计算性能指标
                actual_values = valid_data['真实票房'].astype(float)
                pred_values = valid_data[pred_type].astype(float)
                errors = valid_data[error_col].astype(float)
                
                mae = errors.mean()
                rmse = np.sqrt(np.mean(errors**2))
                mape = np.mean(np.abs((actual_values - pred_values) / actual_values)) * 100
                
                # 检查负值
                negative_count = (pred_values < 0).sum()
                
                performance_data.append({
                    '预测方法': pred_type,
                    '样本数': len(valid_data),
                    'MAE': mae,
                    'RMSE': rmse,
                    'MAPE(%)': mape,
                    '负值数量': negative_count,
                    '预测范围': f"{pred_values.min():.1f} - {pred_values.max():.1f}"
                })
                
                print(f"{pred_type}:")
                print(f"  • 样本数: {len(valid_data)}")
                print(f"  • MAE: {mae:.2f}")
                print(f"  • RMSE: {rmse:.2f}")
                print(f"  • MAPE: {mape:.1f}%")
                print(f"  • 负值数量: {negative_count} ✅")
                print(f"  • 预测范围: {pred_values.min():.1f} - {pred_values.max():.1f}")
                print()
    
    # 保存性能数据
    if performance_data:
        performance_df = pd.DataFrame(performance_data)
        performance_df.to_csv('corrected_performance_summary.csv', index=False, encoding='utf-8-sig')
        print(f"💾 性能汇总已保存到: corrected_performance_summary.csv")
    
    return performance_data

def create_visualization(processed_df):
    """
    创建可视化图表
    """
    print(f"\n📊 创建可视化图表...")
    
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 筛选有预测值的数据
    prediction_mask = (
        (processed_df['7天预测'] != "-") | 
        (processed_df['14天预测'] != "-") | 
        (processed_df['21天预测'] != "-")
    )
    
    plot_data = processed_df[prediction_mask].copy()
    
    if len(plot_data) == 0:
        print("❌ 没有可用的预测数据进行可视化")
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 预测vs实际对比
    ax1 = axes[0, 0]
    dates = range(len(plot_data))
    actual_values = plot_data['真实票房'].astype(float)
    
    ax1.plot(dates, actual_values, 'ko-', linewidth=2, markersize=6, label='真实票房', alpha=0.8)
    
    colors = ['blue', 'red', 'green']
    for i, pred_col in enumerate(['7天预测', '14天预测', '21天预测']):
        if pred_col in plot_data.columns:
            pred_mask = plot_data[pred_col] != "-"
            if pred_mask.any():
                pred_dates = [dates[j] for j in range(len(dates)) if pred_mask.iloc[j]]
                pred_values = [float(plot_data[pred_col].iloc[j]) for j in range(len(plot_data)) if pred_mask.iloc[j]]
                
                ax1.plot(pred_dates, pred_values, 'o--', color=colors[i], 
                        linewidth=2, markersize=6, label=pred_col, alpha=0.8)
    
    ax1.set_title('后处理后的预测结果对比', fontsize=14, fontweight='bold')
    ax1.set_xlabel('时间点')
    ax1.set_ylabel('票房')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 误差分布
    ax2 = axes[0, 1]
    all_errors = []
    error_labels = []
    
    for pred_col in ['7天预测', '14天预测', '21天预测']:
        error_col = pred_col.replace('预测', '误差')
        if error_col in plot_data.columns:
            error_mask = plot_data[error_col] != "-"
            if error_mask.any():
                errors = [float(plot_data[error_col].iloc[j]) for j in range(len(plot_data)) if error_mask.iloc[j]]
                all_errors.extend(errors)
                error_labels.extend([pred_col] * len(errors))
    
    if all_errors:
        ax2.hist(all_errors, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(np.mean(all_errors), color='red', linestyle='--', linewidth=2, 
                   label=f'平均误差: {np.mean(all_errors):.1f}')
        ax2.set_title('预测误差分布', fontsize=14, fontweight='bold')
        ax2.set_xlabel('误差值')
        ax2.set_ylabel('频次')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
    
    # 3. 预测值范围对比
    ax3 = axes[1, 0]
    pred_ranges = []
    pred_names = []
    
    for pred_col in ['7天预测', '14天预测', '21天预测']:
        if pred_col in plot_data.columns:
            pred_mask = plot_data[pred_col] != "-"
            if pred_mask.any():
                pred_values = [float(plot_data[pred_col].iloc[j]) for j in range(len(plot_data)) if pred_mask.iloc[j]]
                pred_ranges.append([min(pred_values), max(pred_values)])
                pred_names.append(pred_col)
    
    if pred_ranges:
        for i, (name, (min_val, max_val)) in enumerate(zip(pred_names, pred_ranges)):
            ax3.barh(i, max_val - min_val, left=min_val, alpha=0.7, 
                    label=f'{name}: {min_val:.1f} - {max_val:.1f}')
        
        ax3.set_title('预测值范围对比', fontsize=14, fontweight='bold')
        ax3.set_xlabel('票房值')
        ax3.set_yticks(range(len(pred_names)))
        ax3.set_yticklabels(pred_names)
        ax3.grid(True, alpha=0.3)
    
    # 4. 性能指标对比
    ax4 = axes[1, 1]
    try:
        perf_df = pd.read_csv('corrected_performance_summary.csv')
        
        methods = perf_df['预测方法'].tolist()
        mae_values = perf_df['MAE'].tolist()
        
        bars = ax4.bar(methods, mae_values, alpha=0.7, color=['blue', 'red', 'green'])
        ax4.set_title('后处理后的MAE对比', fontsize=14, fontweight='bold')
        ax4.set_ylabel('MAE')
        ax4.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, mae in zip(bars, mae_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{mae:.1f}', ha='center', va='bottom')
        
        ax4.grid(True, alpha=0.3)
        
    except FileNotFoundError:
        ax4.text(0.5, 0.5, '性能数据未找到', ha='center', va='center', transform=ax4.transAxes)
    
    plt.tight_layout()
    plt.savefig('corrected_predictions_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表已保存到: corrected_predictions_analysis.png")

def main():
    """
    主函数
    """
    print("🎯 实施推荐方案：原始模型 + 后处理")
    print("="*50)
    
    # 1. 后处理原始预测结果
    processed_df, negative_stats = post_process_original_predictions()
    
    if processed_df is not None:
        # 2. 创建可视化
        create_visualization(processed_df)
        
        # 3. 显示最终总结
        print(f"\n" + "="*50)
        print("✅ 后处理方案实施完成！")
        print("\n📁 生成的文件:")
        print("  1. corrected_prediction_summary.csv - 修正后的预测结果 ⭐ 主要文件")
        print("  2. before_after_comparison.csv - 处理前后对比")
        print("  3. corrected_performance_summary.csv - 修正后的性能汇总")
        print("  4. corrected_predictions_analysis.png - 可视化分析图")
        
        print(f"\n🎯 方案优势:")
        print("  ✅ 完全消除负值问题")
        print("  ✅ 保持预测多样性")
        print("  ✅ 保持相对较好的预测精度")
        print("  ✅ 无需重新训练模型")
        
        print(f"\n💡 使用建议:")
        print("  • 主要使用: corrected_prediction_summary.csv")
        print("  • 这个文件解决了负值问题，同时保持了预测的合理性")
        print("  • 可以放心用于业务决策和分析")
        
        # 显示处理后的预测结果预览
        print(f"\n📊 修正后的预测结果预览:")
        prediction_mask = (
            (processed_df['7天预测'] != "-") | 
            (processed_df['14天预测'] != "-") | 
            (processed_df['21天预测'] != "-")
        )
        preview_data = processed_df[prediction_mask].head(10)
        print(preview_data[['日期', '真实票房', '7天预测', '14天预测', '21天预测']].to_string(index=False))

if __name__ == "__main__":
    main()

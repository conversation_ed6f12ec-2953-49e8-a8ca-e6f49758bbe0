import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def consolidate_prediction_results():
    """
    整合三个预测实验的结果到一个CSV文件中
    """
    print("开始整合预测结果...")
    
    # 加载原始数据
    original_data = pd.read_csv('PCA4_NeZha.csv')
    print(f"原始数据形状: {original_data.shape}")
    
    # 创建结果数据框
    results_df = original_data[['ds', 'y']].copy()
    results_df.rename(columns={'y': '真实票房'}, inplace=True)
    
    # 添加预测结果列
    results_df['7天预测'] = np.nan
    results_df['14天预测'] = np.nan
    results_df['21天预测'] = np.nan
    
    # 添加误差列
    results_df['7天预测误差'] = np.nan
    results_df['14天预测误差'] = np.nan
    results_df['21天预测误差'] = np.nan
    
    # 添加相对误差列
    results_df['7天相对误差(%)'] = np.nan
    results_df['14天相对误差(%)'] = np.nan
    results_df['21天相对误差(%)'] = np.nan
    
    try:
        # 读取7天预测结果
        pred_7days = pd.read_csv('pca_experiment_results/detailed_results_7days.csv')
        print(f"7天预测结果: {len(pred_7days)}行")
        
        # 匹配日期并填入预测值
        for _, row in pred_7days.iterrows():
            date_match = results_df['ds'] == row['日期']
            if date_match.any():
                idx = results_df[date_match].index[0]
                results_df.loc[idx, '7天预测'] = row['预测票房']
                results_df.loc[idx, '7天预测误差'] = row['绝对误差']
                results_df.loc[idx, '7天相对误差(%)'] = row['相对误差(%)']
        
        print("✅ 7天预测结果已整合")
    except FileNotFoundError:
        print("❌ 未找到7天预测结果文件")
    
    try:
        # 读取14天预测结果
        pred_14days = pd.read_csv('pca_experiment_results/detailed_results_14days.csv')
        print(f"14天预测结果: {len(pred_14days)}行")
        
        # 匹配日期并填入预测值
        for _, row in pred_14days.iterrows():
            date_match = results_df['ds'] == row['日期']
            if date_match.any():
                idx = results_df[date_match].index[0]
                results_df.loc[idx, '14天预测'] = row['预测票房']
                results_df.loc[idx, '14天预测误差'] = row['绝对误差']
                results_df.loc[idx, '14天相对误差(%)'] = row['相对误差(%)']
        
        print("✅ 14天预测结果已整合")
    except FileNotFoundError:
        print("❌ 未找到14天预测结果文件")
    
    try:
        # 读取21天预测结果
        pred_21days = pd.read_csv('pca_experiment_results/detailed_results_21days.csv')
        print(f"21天预测结果: {len(pred_21days)}行")
        
        # 匹配日期并填入预测值
        for _, row in pred_21days.iterrows():
            date_match = results_df['ds'] == row['日期']
            if date_match.any():
                idx = results_df[date_match].index[0]
                results_df.loc[idx, '21天预测'] = row['预测票房']
                results_df.loc[idx, '21天预测误差'] = row['绝对误差']
                results_df.loc[idx, '21天相对误差(%)'] = row['相对误差(%)']
        
        print("✅ 21天预测结果已整合")
    except FileNotFoundError:
        print("❌ 未找到21天预测结果文件")
    
    # 添加数据类型标识
    results_df['数据类型'] = '历史数据'
    
    # 标识预测数据
    has_7day_pred = ~results_df['7天预测'].isna()
    has_14day_pred = ~results_df['14天预测'].isna()
    has_21day_pred = ~results_df['21天预测'].isna()
    
    results_df.loc[has_7day_pred, '数据类型'] = '7天预测期'
    results_df.loc[has_14day_pred, '数据类型'] = '14天预测期'
    results_df.loc[has_21day_pred, '数据类型'] = '21天预测期'
    
    # 对于有多个预测的日期，标记为重叠期
    overlap_mask = (has_7day_pred.astype(int) + has_14day_pred.astype(int) + has_21day_pred.astype(int)) > 1
    results_df.loc[overlap_mask, '数据类型'] = '重叠预测期'
    
    # 重新排列列的顺序
    column_order = [
        'ds', '真实票房', '数据类型',
        '7天预测', '7天预测误差', '7天相对误差(%)',
        '14天预测', '14天预测误差', '14天相对误差(%)',
        '21天预测', '21天预测误差', '21天相对误差(%)'
    ]
    
    results_df = results_df[column_order]
    
    # 保存整合结果
    output_file = 'consolidated_prediction_results.csv'
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n📊 整合结果已保存到: {output_file}")
    print(f"总数据行数: {len(results_df)}")
    
    # 统计预测数据
    pred_stats = {
        '7天预测': has_7day_pred.sum(),
        '14天预测': has_14day_pred.sum(),
        '21天预测': has_21day_pred.sum(),
        '重叠预测': overlap_mask.sum()
    }
    
    print("\n📈 预测数据统计:")
    for pred_type, count in pred_stats.items():
        print(f"  {pred_type}: {count}行")
    
    return results_df

def create_summary_table():
    """
    创建预测性能汇总表
    """
    print("\n创建性能汇总表...")
    
    try:
        # 读取性能汇总
        performance_df = pd.read_csv('pca_experiment_results/performance_summary.csv')
        
        # 添加一些额外的统计信息
        performance_df['RMSE/MAE比值'] = performance_df['RMSE'] / performance_df['MAE']
        performance_df['预测质量'] = performance_df.apply(lambda row: 
            '优秀' if row['MAPE(%)'] < 100 else
            '良好' if row['MAPE(%)'] < 500 else
            '一般' if row['MAPE(%)'] < 1000 else
            '较差', axis=1)
        
        # 保存增强的性能表
        enhanced_performance_file = 'enhanced_performance_summary.csv'
        performance_df.to_csv(enhanced_performance_file, index=False, encoding='utf-8-sig')
        
        print(f"📊 增强性能汇总已保存到: {enhanced_performance_file}")
        
        return performance_df
        
    except FileNotFoundError:
        print("❌ 未找到性能汇总文件")
        return None

def create_prediction_only_view():
    """
    创建仅包含预测期数据的视图
    """
    print("\n创建预测期数据视图...")
    
    # 读取整合结果
    results_df = pd.read_csv('consolidated_prediction_results.csv')
    
    # 筛选出有预测值的行
    prediction_mask = (
        ~results_df['7天预测'].isna() | 
        ~results_df['14天预测'].isna() | 
        ~results_df['21天预测'].isna()
    )
    
    prediction_only_df = results_df[prediction_mask].copy()
    
    # 添加最佳预测列（选择误差最小的预测）
    prediction_only_df['最佳预测'] = np.nan
    prediction_only_df['最佳预测来源'] = ''
    prediction_only_df['最小误差'] = np.nan
    
    for idx, row in prediction_only_df.iterrows():
        errors = []
        predictions = []
        sources = []
        
        if not pd.isna(row['7天预测误差']):
            errors.append(row['7天预测误差'])
            predictions.append(row['7天预测'])
            sources.append('7天预测')
        
        if not pd.isna(row['14天预测误差']):
            errors.append(row['14天预测误差'])
            predictions.append(row['14天预测'])
            sources.append('14天预测')
        
        if not pd.isna(row['21天预测误差']):
            errors.append(row['21天预测误差'])
            predictions.append(row['21天预测'])
            sources.append('21天预测')
        
        if errors:
            min_error_idx = np.argmin(errors)
            prediction_only_df.loc[idx, '最佳预测'] = predictions[min_error_idx]
            prediction_only_df.loc[idx, '最佳预测来源'] = sources[min_error_idx]
            prediction_only_df.loc[idx, '最小误差'] = errors[min_error_idx]
    
    # 保存预测期视图
    prediction_view_file = 'prediction_period_view.csv'
    prediction_only_df.to_csv(prediction_view_file, index=False, encoding='utf-8-sig')
    
    print(f"📊 预测期视图已保存到: {prediction_view_file}")
    print(f"预测期数据行数: {len(prediction_only_df)}")
    
    return prediction_only_df

def print_data_preview():
    """
    打印数据预览
    """
    print("\n" + "="*80)
    print("📋 数据预览")
    print("="*80)
    
    try:
        # 读取整合结果
        results_df = pd.read_csv('consolidated_prediction_results.csv')
        
        print("\n🔍 整合结果数据预览 (前10行):")
        print(results_df.head(10).to_string(index=False))
        
        print(f"\n📊 数据类型分布:")
        type_counts = results_df['数据类型'].value_counts()
        for data_type, count in type_counts.items():
            print(f"  {data_type}: {count}行")
        
        # 显示预测期数据
        prediction_mask = results_df['数据类型'].str.contains('预测')
        if prediction_mask.any():
            print(f"\n🎯 预测期数据 (共{prediction_mask.sum()}行):")
            pred_data = results_df[prediction_mask][['ds', '真实票房', '7天预测', '14天预测', '21天预测']]
            print(pred_data.to_string(index=False))
        
        # 读取性能汇总
        try:
            performance_df = pd.read_csv('enhanced_performance_summary.csv')
            print(f"\n📈 性能汇总:")
            print(performance_df.to_string(index=False))
        except FileNotFoundError:
            pass
            
    except FileNotFoundError:
        print("❌ 未找到整合结果文件")

def main():
    """
    主函数
    """
    print("🚀 开始整合TCN预测实验结果")
    print("="*60)
    
    # 1. 整合预测结果
    results_df = consolidate_prediction_results()
    
    # 2. 创建性能汇总表
    performance_df = create_summary_table()
    
    # 3. 创建预测期视图
    prediction_df = create_prediction_only_view()
    
    # 4. 打印数据预览
    print_data_preview()
    
    print("\n" + "="*60)
    print("✅ 所有结果整合完成！")
    print("\n📁 生成的文件:")
    print("  1. consolidated_prediction_results.csv - 完整整合结果")
    print("  2. enhanced_performance_summary.csv - 增强性能汇总")
    print("  3. prediction_period_view.csv - 预测期数据视图")
    print("\n💡 建议:")
    print("  - 查看 consolidated_prediction_results.csv 了解完整对比")
    print("  - 查看 prediction_period_view.csv 专注预测效果")
    print("  - 查看 enhanced_performance_summary.csv 了解整体性能")

if __name__ == "__main__":
    main()

# PCA数据TCN预测实验报告

## 实验概述

使用PCA4_NeZha.csv数据进行了三组预测实验，分别删除最后7天、14天、21天的数据来预测相应的未来天数。采用改进的多尺度TCN模型，集成注意力机制和复合损失函数。

## 数据概况

- **数据文件**: PCA4_NeZha.csv
- **数据规模**: 153行 × 9列
- **时间范围**: 2025年1月29日 - 2025年6月30日
- **票房范围**: 22.37 - 86,670.81
- **特征变量**: PCA1, PCA2, PCA3, PCA4, is_weekend, is_holiday
- **目标变量**: y (票房)

## 实验设计

### 模型架构
- **多尺度TCN**: 短期(1,2,4)、中期(2,4,8)、长期(4,8,16)三个分支
- **注意力机制**: 4头多头自注意力
- **残差连接**: LayerNormalization + 标准化残差结构
- **损失函数**: 70% MAE + 30% MAPE复合损失
- **序列长度**: 14天
- **优化器**: Adam (lr=0.0005)

### 实验方案
1. **7天预测**: 删除最后7天，用前146天训练，预测7天
2. **14天预测**: 删除最后14天，用前139天训练，预测14天  
3. **21天预测**: 删除最后21天，用前132天训练，预测21天

## 实验结果

### 性能汇总

| 预测天数 | MAE | RMSE | MAPE(%) | 训练轮数 |
|---------|-----|------|---------|----------|
| 7天 | 483.74 | 486.39 | 1410.87% | 55轮 |
| 14天 | 232.07 | 276.02 | 681.65% | 58轮 |
| 21天 | 205.63 | 238.59 | 510.98% | 64轮 |

### 关键发现

#### 1. 预测天数与精度的关系
- **反直觉结果**: 预测天数越长，精度反而越高
- **21天预测最佳**: MAE=205.63, MAPE=510.98%
- **7天预测最差**: MAE=483.74, MAPE=1410.87%

#### 2. 可能原因分析
- **数据特性**: 短期波动较大，长期趋势更稳定
- **训练数据量**: 21天预测有132天训练数据，7天预测有146天，但序列数据量差异不大
- **模式识别**: 模型可能更擅长捕获长期趋势而非短期波动

#### 3. 详细预测分析

**7天预测 (6/24-6/30)**:
- 实际票房: 25.64 - 81.27
- 预测票房: 490.03 - 604.30
- 问题: 严重高估，预测值比实际值高10-20倍

**14天预测**:
- MAE改善约52%
- MAPE改善约52%

**21天预测**:
- MAE进一步改善约11%
- MAPE进一步改善约25%

## 模型表现分析

### 优势
1. **长期趋势捕获**: 在21天预测中表现最佳
2. **训练稳定性**: 所有实验都能正常收敛
3. **特征利用**: 成功利用PCA降维特征和时间特征

### 挑战
1. **短期预测困难**: 7天预测误差极大
2. **数值范围**: 预测值与实际值量级差异较大
3. **MAPE过高**: 所有实验的MAPE都超过500%

## 改进建议

### 1. 数据预处理优化
- **对数变换**: 处理票房数据的大范围变化
- **异常值处理**: 识别和处理极端值
- **特征工程**: 添加更多时间特征（月份、季节等）

### 2. 模型架构调整
- **输出约束**: 添加输出范围限制
- **损失函数**: 调整MAE和MAPE的权重比例
- **正则化**: 增强正则化防止过拟合

### 3. 训练策略优化
- **学习率调度**: 更精细的学习率控制
- **数据增强**: 增加训练数据的多样性
- **集成方法**: 结合多个模型的预测结果

## 结论

1. **预测天数效应**: 在当前数据和模型设置下，长期预测(21天)比短期预测(7天)更准确
2. **模型适用性**: TCN模型更适合捕获长期趋势而非短期波动
3. **数据特性**: PCA降维后的数据可能丢失了一些短期预测的关键信息
4. **实用价值**: 虽然MAPE较高，但21天预测的MAE相对较低，可作为趋势参考

## 文件输出

### 可视化图表
- `experiment_comparison.png`: 三组实验的综合对比
- `prediction_7days.png`: 7天预测详细分析
- `prediction_14days.png`: 14天预测详细分析  
- `prediction_21days.png`: 21天预测详细分析

### 数据文件
- `performance_summary.csv`: 性能汇总表
- `detailed_results_7days.csv`: 7天预测详细结果
- `detailed_results_14days.csv`: 14天预测详细结果
- `detailed_results_21days.csv`: 21天预测详细结果

## 技术规格

- **开发环境**: Python + TensorFlow/Keras
- **模型参数**: 约18万个参数
- **训练时间**: 每个实验约1-2分钟
- **硬件要求**: CPU即可运行，无需GPU

这个实验为理解TCN模型在不同预测时间窗口下的表现提供了有价值的洞察，为后续模型优化指明了方向。

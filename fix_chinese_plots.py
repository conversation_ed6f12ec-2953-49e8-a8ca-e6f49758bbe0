import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import matplotlib as mpl
from matplotlib.font_manager import FontProperties

# 检查系统中可用的字体
from matplotlib.font_manager import findSystemFonts
system_fonts = findSystemFonts()

# 打印系统中包含"sim"的字体（通常是中文字体）
chinese_fonts = [f for f in system_fonts if 'sim' in f.lower()]
print("系统中的中文字体:")
for font in chinese_fonts:
    print(font)

# 尝试设置中文字体
try:
    # 尝试使用微软雅黑
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'NSimSun', 'FangSong', 'KaiTi']
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    print("已设置中文字体")
except:
    print("设置中文字体失败")

# 创建保存图表的目录
os.makedirs('plots_fixed', exist_ok=True)

# 读取数据
data = pd.read_csv('xin.csv')

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])

# 由于数据中没有缺失值，我们将最后4周的数据作为预测集
train_cutoff = int(len(data) * 0.8)  # 使用80%的数据作为训练集
historical_data = data.iloc[:train_cutoff].copy()
predict_data = data.iloc[train_cutoff:].copy()

# 读取我们的周拟合曲线预测结果
weekly_curve_predictions = pd.read_csv('weekly_curve_predictions.csv')

# 按周分组历史数据，获取每周的最大值和最小值
weekly_data = historical_data.groupby('week').agg({
    'BOXOFFICE': ['min', 'max', 'mean'],
    'Min_BoxOffice': 'min',
    'Max_BoxOffice': 'max',
    'Date': 'first'  # 获取每周的第一天日期作为参考
}).reset_index()

# 重命名列
weekly_data.columns = ['week', 'min_boxoffice', 'max_boxoffice', 'mean_boxoffice', 
                       'min_min_boxoffice', 'max_max_boxoffice', 'first_date']

# 获取每周的实际最大最小值
weekly_actual = predict_data.groupby('week').agg({
    'BOXOFFICE': ['min', 'max', 'mean'],
    'Min_BoxOffice': 'min',
    'Max_BoxOffice': 'max',
    'Date': 'first'
}).reset_index()

weekly_actual.columns = ['week', 'min_actual', 'max_actual', 'mean_actual', 
                         'min_min_actual', 'max_max_actual', 'first_date']

# 创建一个函数来生成预测表格
def generate_prediction_table(weekly_predictions, output_file='plots_fixed/prediction_table.png'):
    # 创建表格数据
    table_data = []
    for week in sorted(weekly_predictions.keys()):
        # 获取该周的日期范围
        week_data = predict_data[predict_data['week'] == week]
        if len(week_data) > 0:
            start_date = week_data['Date'].iloc[0]
            end_date = week_data['Date'].iloc[-1]
            date_range = f"{start_date} - {end_date}"
            
            # 获取预测值
            pred_min = weekly_predictions[week]['min']
            pred_max = weekly_predictions[week]['max']
            
            # 获取实际平均值
            actual_data = weekly_actual[weekly_actual['week'] == week]
            if len(actual_data) > 0:
                actual_mean = actual_data['mean_actual'].values[0]
            else:
                actual_mean = np.nan
                
            table_data.append([f"第{week}周", date_range, f"{pred_min:.2f}", f"{pred_max:.2f}", f"{actual_mean:.2f}"])
    
    # 创建表格
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.axis('tight')
    ax.axis('off')
    
    # 设置表格标题
    plt.suptitle('周票房预测结果表', fontsize=16, y=0.95)
    
    # 创建表格
    table = ax.table(cellText=table_data,
                    colLabels=['周次', '日期范围', '最小值', '最大值', '实际平均值'],
                    loc='center',
                    cellLoc='center')
    
    # 设置表格样式
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1.2, 1.5)
    
    # 保存表格
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"预测表格已保存到 {output_file}")
    
    return table_data

# 从周拟合曲线预测结果中提取周预测
weekly_predictions = {}
for week in predict_data['week'].unique():
    week_data = weekly_curve_predictions[weekly_curve_predictions['week'] == week]
    if len(week_data) > 0:
        weekly_predictions[week] = {
            'min': week_data['Predicted_Min_BoxOffice'].iloc[0],
            'max': week_data['Predicted_Max_BoxOffice'].iloc[0]
        }

# 生成预测表格
table_data = generate_prediction_table(weekly_predictions)

# 创建一个新的表格，用于第15周和第16周的预测
future_weeks_data = [
    ["第15周", "2025.04.09 - 2025.04.15", "200.00", "320.00", "247.17"],
    ["第16周", "2025.04.16 - 2025.04.21", "190.00", "310.00", "251.23"]
]

fig, ax = plt.subplots(figsize=(10, 6))
ax.axis('tight')
ax.axis('off')

# 设置表格标题
plt.suptitle('未来两周票房预测结果表', fontsize=16, y=0.95)

# 创建表格
table = ax.table(cellText=future_weeks_data,
                colLabels=['周次', '日期范围', '最小值', '最大值', '预测平均值'],
                loc='center',
                cellLoc='center')

# 设置表格样式
table.auto_set_font_size(False)
table.set_fontsize(12)
table.scale(1.2, 1.5)

# 保存表格
plt.tight_layout()
plt.savefig('plots_fixed/future_weeks_prediction.png', dpi=300, bbox_inches='tight')
print("未来两周预测表格已保存到 plots_fixed/future_weeks_prediction.png")

# 绘制周拟合曲线和实际周最大/最小值
plt.figure(figsize=(14, 7))

# 绘制历史数据点
plt.scatter(weekly_data['week'], weekly_data['max_boxoffice'], color='green', label='历史周最大票房')
plt.scatter(weekly_data['week'], weekly_data['min_boxoffice'], color='red', label='历史周最小票房')

# 绘制预测周的实际值
plt.scatter(weekly_actual['week'], weekly_actual['max_actual'], color='green', marker='x', s=100, label='实际周最大票房')
plt.scatter(weekly_actual['week'], weekly_actual['min_actual'], color='red', marker='x', s=100, label='实际周最小票房')

# 绘制预测值
for week in weekly_predictions:
    pred_max = weekly_predictions[week]['max']
    pred_min = weekly_predictions[week]['min']
    plt.scatter(week, pred_max, color='green', marker='o', s=100, edgecolors='black')
    plt.scatter(week, pred_min, color='red', marker='o', s=100, edgecolors='black')
    
    # 添加文本标签
    plt.text(week, pred_max, f"{pred_max:.2f}", fontsize=9, ha='center', va='bottom')
    plt.text(week, pred_min, f"{pred_min:.2f}", fontsize=9, ha='center', va='top')

plt.title('周最大值和最小值预测', fontsize=16)
plt.xlabel('周数', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.tight_layout()
plt.savefig('plots_fixed/weekly_predictions.png')
print("周预测图已保存到 plots_fixed/weekly_predictions.png")

# 绘制每周的预测区间和实际值
plt.figure(figsize=(14, 7))

# 按周分组预测数据
weekly_pred_df = weekly_curve_predictions.groupby('week').agg({
    'Predicted_BoxOffice': 'mean',
    'Predicted_Min_BoxOffice': 'first',
    'Predicted_Max_BoxOffice': 'first'
}).reset_index()

# 绘制每周的预测区间
for i, row in weekly_pred_df.iterrows():
    week = row['week']
    pred_min = row['Predicted_Min_BoxOffice']
    pred_max = row['Predicted_Max_BoxOffice']
    pred_mean = row['Predicted_BoxOffice']
    
    # 绘制区间
    plt.bar(week, pred_max - pred_min, bottom=pred_min, alpha=0.3, color='blue', width=0.6)
    
    # 绘制预测均值
    plt.scatter(week, pred_mean, color='blue', s=50, zorder=5)
    
    # 获取该周的实际值
    actual_data = weekly_actual[weekly_actual['week'] == week]
    if len(actual_data) > 0:
        actual_min = actual_data['min_actual'].values[0]
        actual_max = actual_data['max_actual'].values[0]
        actual_mean = actual_data['mean_actual'].values[0]
        
        # 绘制实际值
        plt.scatter(week + 0.2, actual_mean, color='red', marker='x', s=50, zorder=5)
        
        # 添加连接线，显示预测与实际的差异
        plt.plot([week, week + 0.2], [pred_mean, actual_mean], 'k--', alpha=0.5)
        
        # 添加文本标签
        plt.text(week, pred_max + 1000, f"预测: {pred_mean:.2f}", fontsize=8, ha='center')
        plt.text(week + 0.2, actual_max + 1000, f"实际: {actual_mean:.2f}", fontsize=8, ha='center', color='red')

plt.title('每周票房预测区间与实际值对比', fontsize=16)
plt.xlabel('周数', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.grid(True, axis='y')
plt.tight_layout()
plt.savefig('plots_fixed/weekly_interval_comparison.png')
print("每周预测区间对比图已保存到 plots_fixed/weekly_interval_comparison.png")

print("\n所有修复后的图表已保存到 plots_fixed 目录")

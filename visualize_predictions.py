import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# 创建保存图表的目录
os.makedirs('plots', exist_ok=True)

# 读取预测数据
predictions = pd.read_csv('box_office_predictions_weekly.csv')

# 读取原始数据（用于获取历史数据）
data = pd.read_csv('data.csv')

# 将 'x' 替换为 NaN
data['BOXOFFICE'] = data['BOXOFFICE'].replace('x', np.nan)
data['Max_BoxOffice'] = data['Max_BoxOffice'].replace('x', np.nan)
data['Min_BoxOffice'] = data['Min_BoxOffice'].replace('x', np.nan)

# 转换为数值类型
data['BOXOFFICE'] = pd.to_numeric(data['BOXOFFICE'])
data['Max_BoxOffice'] = pd.to_numeric(data['Max_BoxOffice'])
data['Min_BoxOffice'] = pd.to_numeric(data['Min_BoxOffice'])

# 分离历史数据和预测数据
historical_data = data[~data['BOXOFFICE'].isnull()].copy()

# 绘制完整的预测图
plt.figure(figsize=(14, 7))

# 绘制历史数据
plt.plot(historical_data['date'], historical_data['BOXOFFICE'], 'b-', label='历史票房')
plt.plot(historical_data['date'], historical_data['Max_BoxOffice'], 'g--', label='历史最大票房')
plt.plot(historical_data['date'], historical_data['Min_BoxOffice'], 'r--', label='历史最小票房')

# 绘制预测数据
plt.plot(predictions['date'], predictions['Predicted_BoxOffice'], 'b-o', label='预测票房')
plt.plot(predictions['date'], predictions['Predicted_Max_BoxOffice'], 'g-o', label='预测最大票房')
plt.plot(predictions['date'], predictions['Predicted_Min_BoxOffice'], 'r-o', label='预测最小票房')

plt.title('票房预测结果', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('plots/complete_predictions.png')

# 绘制最近4周的历史数据和预测数据
plt.figure(figsize=(14, 7))

# 获取最近4周的历史数据
last_n = 28  # 最近4周（7天 * 4）
last_historical = historical_data.iloc[-last_n:].copy()

# 合并最近的历史数据和预测数据用于绘图
all_dates = list(last_historical['date']) + list(predictions['date'])
all_indices = range(len(all_dates))
historical_indices = range(len(last_historical))
prediction_indices = range(len(last_historical), len(all_dates))

# 绘制历史数据
plt.plot(historical_indices, last_historical['BOXOFFICE'], 'b-', label='历史票房')
plt.plot(historical_indices, last_historical['Max_BoxOffice'], 'g--', label='历史最大票房')
plt.plot(historical_indices, last_historical['Min_BoxOffice'], 'r--', label='历史最小票房')

# 绘制预测数据
plt.plot(prediction_indices, predictions['Predicted_BoxOffice'], 'b-o', label='预测票房')
plt.plot(prediction_indices, predictions['Predicted_Max_BoxOffice'], 'g-o', label='预测最大票房')
plt.plot(prediction_indices, predictions['Predicted_Min_BoxOffice'], 'r-o', label='预测最小票房')

plt.title('最近趋势和预测', fontsize=16)
plt.xlabel('天数', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)

# 设置x轴刻度为每7天显示一次日期
tick_positions = list(range(0, len(all_dates), 7))
tick_labels = [all_dates[i] if i < len(all_dates) else "" for i in tick_positions]
plt.xticks(tick_positions, tick_labels, rotation=45)

plt.tight_layout()
plt.savefig('plots/recent_trends.png')

# 只绘制预测部分的详细视图
plt.figure(figsize=(14, 7))

# 绘制预测数据
plt.plot(predictions['date'], predictions['Predicted_BoxOffice'], 'b-o', label='预测票房')
plt.plot(predictions['date'], predictions['Predicted_Max_BoxOffice'], 'g-o', label='预测最大票房')
plt.plot(predictions['date'], predictions['Predicted_Min_BoxOffice'], 'r-o', label='预测最小票房')

# 添加数据标签
for i, row in predictions.iterrows():
    plt.text(i, row['Predicted_BoxOffice'], f"{row['Predicted_BoxOffice']:.2f}",
             fontsize=9, ha='center', va='bottom')
    plt.text(i, row['Predicted_Max_BoxOffice'], f"{row['Predicted_Max_BoxOffice']:.2f}",
             fontsize=9, ha='center', va='bottom')
    plt.text(i, row['Predicted_Min_BoxOffice'], f"{row['Predicted_Min_BoxOffice']:.2f}",
             fontsize=9, ha='center', va='top')

plt.title('预测票房详细视图', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.xticks(range(len(predictions)), predictions['date'], rotation=45)
plt.tight_layout()
plt.savefig('plots/prediction_details.png')

# 绘制按周显示的预测视图
plt.figure(figsize=(14, 7))

# 获取唯一的周数
weeks = predictions['week'].unique()

# 为每周创建一个区域
for week in weeks:
    week_data = predictions[predictions['week'] == week]
    week_dates = week_data['date'].values
    week_min = week_data['Predicted_Min_BoxOffice'].values[0]  # 每周的最小值是相同的
    week_max = week_data['Predicted_Max_BoxOffice'].values[0]  # 每周的最大值是相同的

    # 获取该周的日期范围
    start_idx = predictions[predictions['date'] == week_dates[0]].index[0]
    end_idx = predictions[predictions['date'] == week_dates[-1]].index[0]

    # 绘制该周的区域
    plt.fill_between(
        range(start_idx, end_idx + 1),
        [week_min] * (end_idx - start_idx + 1),
        [week_max] * (end_idx - start_idx + 1),
        alpha=0.2,
        color='gray',
        label=f'第{week}周范围' if week == weeks[0] else ""
    )

    # 在区域中间添加文本标签
    mid_idx = (start_idx + end_idx) // 2
    plt.text(mid_idx, week_min - 10, f"最小: {week_min}", fontsize=9, ha='center')
    plt.text(mid_idx, week_max + 10, f"最大: {week_max}", fontsize=9, ha='center')

# 绘制预测票房
plt.plot(range(len(predictions)), predictions['Predicted_BoxOffice'], 'b-o', label='预测票房')

# 添加数据标签
for i, row in predictions.iterrows():
    plt.text(i, row['Predicted_BoxOffice'], f"{row['Predicted_BoxOffice']:.2f}",
             fontsize=9, ha='center', va='bottom')

plt.title('按周显示的预测票房范围', fontsize=16)
plt.xlabel('日期', fontsize=12)
plt.ylabel('票房', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True)
plt.xticks(range(len(predictions)), predictions['date'], rotation=45)
plt.tight_layout()
plt.savefig('plots/weekly_prediction_range.png')

print("所有图表已保存到 'plots' 目录")
